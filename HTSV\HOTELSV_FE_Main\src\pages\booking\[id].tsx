import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import Navbar from '../../components/Navbar';
import styles from '../../styles/booking.module.css';
import { toast } from 'react-toastify';
import { bookingService, roomServiceApi } from '../../services/apiService';
import { format } from 'date-fns';

// Import các service từ adminService
import { bookingServiceApi } from '../../services/adminService';

interface Booking {
  bookingId: number;
  customerId: number;
  customerName: string;
  checkInDate: string;
  checkOutDate: string;
  roomType: string;
  roomNumber: string;
  totalAmount: number;
  status: string;
  createdAt: string;
  notes: string;
}

interface BookingService {
  bookingServiceId: number;
  bookingId: number;
  serviceId: number;
  serviceName: string;
  quantity: number;
  price: number;
  totalPrice: number;
}

interface Service {
  serviceId: number;
  name: string;
  description: string;
  price: number;
  category: string;
}

export default function BookingDetailPage() {
  const router = useRouter();
  const { id } = router.query;
  const { isAuthenticated } = useAuth();
  const [booking, setBooking] = useState<Booking | null>(null);
  const [bookingServices, setBookingServices] = useState<BookingService[]>([]);
  const [availableServices, setAvailableServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddServiceModal, setShowAddServiceModal] = useState(false);
  const [selectedService, setSelectedService] = useState<number>(0);
  const [quantity, setQuantity] = useState<number>(1);

  useEffect(() => {
    if (!isAuthenticated) {
      toast.error('Vui lòng đăng nhập để xem chi tiết đặt phòng');
      router.push('/auth/login');
      return;
    }

    if (id) {
      fetchBookingDetails();
      fetchBookingServices();
      fetchAvailableServices();
    }
  }, [isAuthenticated, id, router]);

  const fetchBookingDetails = async () => {
    try {
      setLoading(true);
      console.log('Fetching booking details for ID:', id);

      const response = await bookingService.getBookingById(Number(id));
      console.log('Booking details response:', response);

      if (response && response.success && response.data) {
        // Bỏ qua kiểm tra quyền truy cập trong môi trường phát triển
        // Trong môi trường production, nên bật lại kiểm tra này

        // Lưu ý: Trong thực tế, nên kiểm tra quyền truy cập
        // Ví dụ:
        // if (response.data && response.data.customerId !== (user as any).id) {
        //   toast.error('Bạn không có quyền xem đặt phòng này');
        //   router.push('/booking');
        //   return;
        // }

        // Chuyển đổi dữ liệu từ API sang định dạng Booking
        const bookingData: Booking = {
          bookingId: response.data.bookingId,
          customerId: response.data.customerId,
          customerName: response.data.customerName || 'Khách hàng',
          checkInDate: response.data.checkInDate || response.data.bookingDate,
          checkOutDate: response.data.checkOutDate,
          roomType: response.data.roomType || response.data.name,
          roomNumber: response.data.roomNumber || '',
          totalAmount: response.data.totalAmount,
          status: response.data.status || response.data.bookingStatus,
          createdAt: response.data.bookingDate || response.data.createdAt || new Date().toISOString(),
          notes: response.data.notes || ''
        };

        setBooking(bookingData);
      } else {
        console.error('Invalid response format:', response);
        toast.error('Không thể tải thông tin đặt phòng: Dữ liệu không đúng định dạng');
        router.push('/booking');
      }
    } catch (error: any) {
      console.error('Error fetching booking details:', error);
      toast.error(error.message || 'Không thể tải thông tin đặt phòng');
      router.push('/booking');
    } finally {
      setLoading(false);
    }
  };

  const fetchBookingServices = async () => {
    try {
      // Trong thực tế, nên có API riêng để lấy dịch vụ theo bookingId
      // Tạm thời để trống vì chưa có API
      setBookingServices([]);
    } catch (error: any) {
      console.error('Error fetching booking services:', error);
      toast.error('Không thể tải danh sách dịch vụ đặt phòng');
    }
  };

  const fetchAvailableServices = async () => {
    try {
      // Sử dụng API service mới để lấy danh sách dịch vụ
      const response = await roomServiceApi.getAllServices();

      if (response && response.success && Array.isArray(response.data)) {
        setAvailableServices(response.data);
        if (response.data.length > 0) {
          setSelectedService(response.data[0].serviceId);
        }
      } else {
        toast.error('Không thể tải danh sách dịch vụ: Dữ liệu không đúng định dạng');
        setAvailableServices([]);
      }
    } catch (error: any) {
      console.error('Error fetching available services:', error);
      toast.error('Không thể tải danh sách dịch vụ');
      setAvailableServices([]);
    }
  };

  const handleAddService = async () => {
    if (!selectedService || quantity <= 0) {
      toast.error('Vui lòng chọn dịch vụ và số lượng hợp lệ');
      return;
    }

    try {
      const serviceData = {
        bookingId: Number(id),
        serviceId: selectedService,
        quantity: quantity,
        // Không cần requestedBy nữa vì đã loại bỏ biến user
        requestedBy: booking?.customerId || 0
      };

      const response = await bookingServiceApi.addBookingService(serviceData);
      if (response.success) {
        toast.success('Thêm dịch vụ thành công');
        fetchBookingServices();
        setShowAddServiceModal(false);
        setQuantity(1);
      }
    } catch (error: any) {
      toast.error(error.message || 'Không thể thêm dịch vụ');
    }
  };

  const handleRemoveService = async (bookingServiceId: number) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')) {
      return;
    }

    try {
      const response = await bookingServiceApi.deleteBookingService(bookingServiceId);
      if (response.success) {
        toast.success('Xóa dịch vụ thành công');
        fetchBookingServices();
      }
    } catch (error: any) {
      toast.error(error.message || 'Không thể xóa dịch vụ');
    }
  };

  const cancelBooking = async () => {
    if (!window.confirm('Bạn có chắc chắn muốn hủy đặt phòng này?')) {
      return;
    }

    try {
      const response = await bookingService.updateBookingStatus(Number(id), {
        status: 'Cancelled',
        paymentStatus: 'Cancelled'
      });

      if (response.success) {
        toast.success('Hủy đặt phòng thành công');
        fetchBookingDetails();
      } else {
        throw new Error(response.message || 'Không thể hủy đặt phòng');
      }
    } catch (error: any) {
      console.error('Error cancelling booking:', error);
      toast.error(error.message || 'Không thể hủy đặt phòng');
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch (error) {
      return dateString;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return styles.statusConfirmed;
      case 'pending':
        return styles.statusPending;
      case 'cancelled':
        return styles.statusCancelled;
      case 'completed':
        return styles.statusCompleted;
      default:
        return '';
    }
  };

  if (!isAuthenticated || loading) {
    return (
      <>
        <Navbar />
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      </>
    );
  }

  if (!booking) {
    return (
      <>
        <Navbar />
        <div className={styles.errorContainer}>
          <h2>Không tìm thấy thông tin đặt phòng</h2>
          <button
            className={styles.backButton}
            onClick={() => router.push('/booking')}
          >
            Quay lại
          </button>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Chi tiết đặt phòng | Hotel Nhóm 1</title>
      </Head>
      <Navbar />
      <div className={styles.pageContainer}>
        <div className={styles.bookingDetailHeader}>
          <button
            className={styles.backButton}
            onClick={() => router.push('/booking')}
          >
            &larr; Quay lại
          </button>
          <h1>Chi tiết đặt phòng #{booking.bookingId}</h1>
          <span className={`${styles.statusBadge} ${getStatusClass(booking.status)}`}>
            {booking.status}
          </span>
        </div>

        <div className={styles.bookingDetailContent}>
          <div className={styles.bookingInfo}>
            <h2>Thông tin đặt phòng</h2>
            <div className={styles.infoCard}>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Loại phòng:</span>
                <span className={styles.detailValue}>{booking.roomType}</span>
              </div>
              {booking.roomNumber && (
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Số phòng:</span>
                  <span className={styles.detailValue}>{booking.roomNumber}</span>
                </div>
              )}
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Ngày nhận phòng:</span>
                <span className={styles.detailValue}>{formatDate(booking.checkInDate)}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Ngày trả phòng:</span>
                <span className={styles.detailValue}>{formatDate(booking.checkOutDate)}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Tổng tiền:</span>
                <span className={styles.detailValue}>${booking.totalAmount}</span>
              </div>
              <div className={styles.detailRow}>
                <span className={styles.detailLabel}>Ngày đặt:</span>
                <span className={styles.detailValue}>{formatDate(booking.createdAt)}</span>
              </div>
              {booking.notes && (
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Ghi chú:</span>
                  <span className={styles.detailValue}>{booking.notes}</span>
                </div>
              )}
            </div>

            {(booking.status === 'Pending' || booking.status === 'Confirmed') && (
              <button
                className={styles.cancelButton}
                onClick={cancelBooking}
              >
                Hủy đặt phòng
              </button>
            )}
          </div>

          <div className={styles.bookingServices}>
            <div className={styles.servicesHeader}>
              <h2>Dịch vụ đã đặt</h2>
              {(booking.status === 'Pending' || booking.status === 'Confirmed') && (
                <button
                  className={styles.addServiceButton}
                  onClick={() => setShowAddServiceModal(true)}
                >
                  Thêm dịch vụ
                </button>
              )}
            </div>

            {bookingServices.length === 0 ? (
              <div className={styles.emptyServices}>
                <p>Chưa có dịch vụ nào được đặt</p>
              </div>
            ) : (
              <div className={styles.servicesList}>
                {bookingServices.map((service) => (
                  <div key={service.bookingServiceId} className={styles.serviceCard}>
                    <div className={styles.serviceInfo}>
                      <h3>{service.serviceName}</h3>
                      <div className={styles.serviceDetails}>
                        <span>Số lượng: {service.quantity}</span>
                        <span>Giá: ${service.price}</span>
                        <span>Tổng: ${service.totalPrice}</span>
                      </div>
                    </div>
                    {(booking.status === 'Pending' || booking.status === 'Confirmed') && (
                      <button
                        className={styles.removeServiceButton}
                        onClick={() => handleRemoveService(service.bookingServiceId)}
                      >
                        Xóa
                      </button>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Modal thêm dịch vụ */}
      {showAddServiceModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2>Thêm dịch vụ</h2>
              <button
                className={styles.closeButton}
                onClick={() => setShowAddServiceModal(false)}
              >
                &times;
              </button>
            </div>
            <div className={styles.modalBody}>
              <div className={styles.formGroup}>
                <label>Dịch vụ</label>
                <select
                  value={selectedService}
                  onChange={(e) => setSelectedService(Number(e.target.value))}
                >
                  {availableServices.map((service) => (
                    <option key={service.serviceId} value={service.serviceId}>
                      {service.name} - ${service.price}
                    </option>
                  ))}
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Số lượng</label>
                <input
                  type="number"
                  min="1"
                  value={quantity}
                  onChange={(e) => setQuantity(Number(e.target.value))}
                />
              </div>
            </div>
            <div className={styles.modalFooter}>
              <button
                className={styles.cancelButton}
                onClick={() => setShowAddServiceModal(false)}
              >
                Hủy
              </button>
              <button
                className={styles.confirmButton}
                onClick={handleAddService}
              >
                Thêm dịch vụ
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
