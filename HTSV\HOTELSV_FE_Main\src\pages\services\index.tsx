import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import Navbar from '../../components/Navbar';
import styles from '../../styles/services.module.css';
import { toast } from 'react-toastify';
import { serviceApi } from '../../services/adminService';
import Image from 'next/image';

interface Service {
  serviceId: number;
  name: string;
  description: string;
  price: number;
  category: string;
  isActive: boolean;
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all'); // all, spa, food, etc.
  const { isAuthenticated } = useAuth();
  const router = useRouter();

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await serviceApi.getAllServices();
      if (response.success) {
        setServices(response.data.filter((service: Service) => service.isActive));
      }
    } catch (error: any) {
      toast.error(error.message || 'Không thể tải danh sách dịch vụ');
    } finally {
      setLoading(false);
    }
  };

  const getServiceImage = (category: string) => {
    switch (category.toLowerCase()) {
      case 'spa':
        return '/services/spa.jpg';
      case 'food':
        return '/services/food.jpg';
      case 'transport':
        return '/services/transport.jpg';
      case 'laundry':
        return '/services/laundry.jpg';
      case 'entertainment':
        return '/services/entertainment.jpg';
      default:
        return '/services/default.jpg';
    }
  };

  const getUniqueCategories = () => {
    const categories = services.map(service => service.category);
    return ['all', ...new Set(categories)];
  };

  const filteredServices = services.filter(service => {
    if (filter === 'all') return true;
    return service.category.toLowerCase() === filter.toLowerCase();
  });

  return (
    <>
      <Head>
        <title>Dịch vụ | Hotel Nhóm 1</title>
      </Head>
      <Navbar />
      <div className={styles.pageContainer}>
        <div className={styles.servicesHeader}>
          <h1>Dịch vụ của chúng tôi</h1>
          <p>Khám phá các dịch vụ cao cấp tại Hotel Nhóm 1</p>
        </div>

        <div className={styles.filterTabs}>
          {getUniqueCategories().map((category) => (
            <button
              key={category}
              className={`${styles.filterTab} ${filter === category ? styles.active : ''}`}
              onClick={() => setFilter(category)}
            >
              {category === 'all' ? 'Tất cả' : category}
            </button>
          ))}
        </div>

        {loading ? (
          <div className={styles.loadingContainer}>
            <div className={styles.spinner}></div>
          </div>
        ) : filteredServices.length === 0 ? (
          <div className={styles.emptyState}>
            <h2>Không có dịch vụ nào</h2>
            <p>Vui lòng thử lại sau</p>
          </div>
        ) : (
          <div className={styles.servicesGrid}>
            {filteredServices.map((service) => (
              <div key={service.serviceId} className={styles.serviceCard}>
                <div className={styles.serviceImageContainer}>
                  <Image
                    src={getServiceImage(service.category)}
                    alt={service.name}
                    layout="fill"
                    objectFit="cover"
                    className={styles.serviceImage}
                  />
                  <div className={styles.serviceCategory}>
                    <span className={styles.categoryBadge}>
                      {service.category}
                    </span>
                  </div>
                </div>

                <div className={styles.serviceContent}>
                  <h3 className={styles.serviceName}>{service.name}</h3>
                  <p className={styles.serviceDescription}>{service.description}</p>
                  
                  <div className={styles.serviceFooter}>
                    <div className={styles.priceInfo}>
                      <span className={styles.priceValue}>${service.price}</span>
                    </div>
                    {isAuthenticated ? (
                      <button
                        className={styles.bookButton}
                        onClick={() => router.push('/booking')}
                      >
                        Đặt dịch vụ
                      </button>
                    ) : (
                      <button
                        className={styles.bookButton}
                        onClick={() => router.push('/auth/login')}
                      >
                        Đăng nhập để đặt
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
}
