using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using HOTELSV_BE.Models;
using System.Data.SqlClient;
using Dapper;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class PermissionController : ControllerBase
    {
        private readonly string _connectionString;

        public PermissionController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpPost("Add")]
        [Authorize]
        public async Task<ActionResult<AddPermissionResponse>> AddPermission([FromBody] AddPermissionRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new { Description = request.Description };
                    var result = await connection.QueryFirstOrDefaultAsync<int>(
                        "sp_AddPermission",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new AddPermissionResponse
                    {
                        Success = true,
                        Message = "Permission added successfully",
                        PermissionId = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new AddPermissionResponse
                {
                    Success = false,
                    Message = $"Error adding permission: {ex.Message}"
                });
            }
        }

        [HttpDelete("{id}")]
        [Authorize]
        public async Task<ActionResult<DeletePermissionResponse>> DeletePermission(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new { PermissionId = id };
                    await connection.ExecuteAsync(
                        "sp_DeletePermission",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new DeletePermissionResponse
                    {
                        Success = true,
                        Message = "Permission deleted successfully"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new DeletePermissionResponse
                {
                    Success = false,
                    Message = $"Error deleting permission: {ex.Message}"
                });
            }
        }

        [HttpGet]
        [Authorize]
        public async Task<ActionResult<GetAllPermissionsResponse>> GetAllPermissions()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var permissions = await connection.QueryAsync<Permission>(
                        "sp_GetAllPermissions",
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new GetAllPermissionsResponse
                    {
                        Success = true,
                        Message = "Permissions retrieved successfully",
                        Permissions = permissions.ToList()
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetAllPermissionsResponse
                {
                    Success = false,
                    Message = $"Error retrieving permissions: {ex.Message}"
                });
            }
        }

        [HttpGet("{id}")]
        [Authorize]
        public async Task<ActionResult<GetPermissionResponse>> GetPermissionById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new { PermissionId = id };
                    var permission = await connection.QueryFirstOrDefaultAsync<Permission>(
                        "sp_GetPermissionById",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (permission == null)
                    {
                        return NotFound(new GetPermissionResponse
                        {
                            Success = false,
                            Message = "Permission not found"
                        });
                    }

                    return Ok(new GetPermissionResponse
                    {
                        Success = true,
                        Message = "Permission retrieved successfully",
                        Permission = permission
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetPermissionResponse
                {
                    Success = false,
                    Message = $"Error retrieving permission: {ex.Message}"
                });
            }
        }

        [HttpPut("Update")]
        [Authorize]
        public async Task<ActionResult<UpdatePermissionResponse>> UpdatePermission([FromBody] UpdatePermissionRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new 
                    { 
                        PermissionId = request.PermissionId,
                        PermissionName = request.PermissionName,
                        Description = request.Description
                    };

                    var updatedPermission = await connection.QueryFirstOrDefaultAsync<Permission>(
                        "sp_UpdatePermission",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (updatedPermission == null)
                    {
                        return NotFound(new UpdatePermissionResponse
                        {
                            Success = false,
                            Message = "Permission not found",
                            Permission = null
                        });
                    }

                    return Ok(new UpdatePermissionResponse
                    {
                        Success = true,
                        Message = "Permission updated successfully",
                        Permission = updatedPermission
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new UpdatePermissionResponse
                {
                    Success = false,
                    Message = $"Error updating permission: {ex.Message}",
                    Permission = null
                });
            }
        }
    }
}
