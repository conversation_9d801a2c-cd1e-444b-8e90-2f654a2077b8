import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import Sidebar from '../../components/admin/Sidebar';
import RoomManagement from '../../components/admin/RoomManagement';
import RoomTypeManagement from '../../components/admin/RoomTypeManagement';
import BookingManagement from '../../components/admin/BookingManagement';
import RoomServiceManagement from '../../components/admin/RoomServiceManagement';
import BookingServiceManagement from '../../components/admin/BookingServiceManagement';
import UserManagement from '../../components/admin/UserManagement';
import styles from '../../styles/admin.module.css';
import { toast } from 'react-toastify';

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('rooms');
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Kiểm tra đăng nhập và quyền admin
    if (!isAuthenticated) {
      toast.error('Vui lòng đăng nhập để truy cập trang quản trị');
      // Sử dụng setTimeout để tránh vấn đề với Next.js
      setTimeout(() => {
        router.push('/auth/login');
      }, 100);
      return;
    }

    // Kiểm tra quyền admin
    if (!user?.roles?.includes('Administrator')) {
      toast.error('Bạn không có quyền truy cập trang quản trị');
      setTimeout(() => {
        router.push('/');
      }, 100);
    }
  }, [isAuthenticated, user, router]);

  const renderContent = () => {
    switch (activeTab) {
      case 'rooms':
        return <RoomManagement />;
      case 'roomTypes':
        return <RoomTypeManagement />;
      case 'bookings':
        return <BookingManagement />;
      case 'roomServices':
        return <RoomServiceManagement />;
      case 'bookingServices':
        return <BookingServiceManagement />;
      case 'users':
        return <UserManagement />;
      default:
        return <RoomManagement />;
    }
  };

  const getTitle = () => {
    switch (activeTab) {
      case 'rooms':
        return 'Quản lý phòng';
      case 'roomTypes':
        return 'Quản lý loại phòng';
      case 'bookings':
        return 'Quản lý đặt phòng';
      case 'roomServices':
        return 'Quản lý dịch vụ phòng';
      case 'bookingServices':
        return 'Quản lý dịch vụ đặt phòng';
      case 'users':
        return 'Quản lý người dùng';
      default:
        return 'Quản trị hệ thống';
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>{getTitle()} | Hotel Nhóm 1</title>
      </Head>
      <div className={styles.adminContainer}>
        <Sidebar activeTab={activeTab} setActiveTab={setActiveTab} />
        <main className={styles.mainContent}>
          <div className={styles.header}>
            <h1>{getTitle()}</h1>
            <div className={styles.userInfo}>
              <span>Xin chào, {user?.userName || 'Admin'}</span>
            </div>
          </div>
          <div className={styles.contentArea}>
            {renderContent()}
          </div>
        </main>
      </div>
    </>
  );
}
