.adminContainer {
  display: flex;
  min-height: 100vh;
  background-color: #121212;
  color: #fff;
}

.mainContent {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header h1 {
  font-size: 1.8rem;
  font-weight: 600;
  color: #fff;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.userInfo span {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
}

.contentArea {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 10px;
  padding: 20px;
  min-height: calc(100vh - 150px);
}

/* Sidebar styles */
.sidebar {
  width: 250px;
  background-color: rgba(0, 0, 0, 0.3);
  padding: 20px 0;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
  padding: 0 20px 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 1.5rem;
  font-weight: 700;
  color: #fff;
  text-align: center;
}

.navItem {
  padding: 12px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.navItem:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.navItem.active {
  background-color: rgba(255, 255, 255, 0.15);
  color: #fff;
  border-left: 3px solid #fff;
}

.navIcon {
  font-size: 1.2rem;
}

/* Table styles */
.tableContainer {
  width: 100%;
  overflow-x: auto;
}

.dataTable {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.dataTable th,
.dataTable td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dataTable th {
  background-color: rgba(0, 0, 0, 0.2);
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
}

.dataTable tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Form styles */
.formContainer {
  background-color: rgba(255, 255, 255, 0.05);
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.formTitle {
  margin-bottom: 20px;
  font-size: 1.2rem;
  font-weight: 600;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
  margin-bottom: 1.5rem;
}

.formGroup label {
  color: white;
  font-size: 0.9rem;
}

.formGroup input,
.formGroup select,
.formGroup textarea {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.8rem;
  color: white;
  font-size: 0.9rem;
  width: 100%;
}

/* Fix for dropdown option text color */
.formGroup select option {
  background-color: #121212;
  color: white;
}

.formGroup input:focus,
.formGroup select:focus,
.formGroup textarea:focus {
  outline: none;
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 0 2px rgba(192, 160, 128, 0.2);
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
}

.formRow {
  display: flex;
  gap: 15px;
}

.formRow .formGroup {
  flex: 1;
}

.buttonContainer {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
  justify-content: flex-end;
}

.button {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.primaryButton {
  background: var(--primary-color, #C0A080);
  color: white;
}

.primaryButton:hover {
  background: rgba(192, 160, 128, 0.8);
  transform: translateY(-2px);
}

.secondaryButton {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.secondaryButton:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.dangerButton {
  background-color: #e53935;
  color: white;
}

.dangerButton:hover {
  background-color: #c62828;
}

.successButton {
  background-color: #43a047;
  color: white;
}

.successButton:hover {
  background-color: #388e3c;
}

/* Action buttons in tables */
.actionButton {
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 5px;
  color: rgba(255, 255, 255, 0.7);
  transition: color 0.3s ease;
}

.editButton:hover {
  color: #4a90e2;
}

.deleteButton:hover {
  color: #e53935;
}

.viewButton:hover {
  color: #43a047;
}

/* Search and filter section */
.toolbarSection {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.searchBox {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  padding: 5px 10px;
  width: 300px;
}

.searchBox input {
  background: transparent;
  border: none;
  color: white;
  padding: 5px;
  width: 100%;
}

.searchBox input:focus {
  outline: none;
}

.filterContainer {
  display: flex;
  gap: 10px;
}

.filterSelect {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: white;
  padding: 5px 10px;
}

.filterSelect option {
  background-color: #121212;
  color: white;
}

/* Modal styles */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.modal {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
  animation: modalFadeIn 0.3s ease;
  backdrop-filter: blur(10px);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modalTitle {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
}

.closeButton {
  position: absolute;
  right: 1rem;
  top: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Status badges */
.statusBadge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.statusAvailable {
  background-color: rgba(67, 160, 71, 0.2);
  color: #81c784;
}

.statusBooked {
  background-color: rgba(229, 57, 53, 0.2);
  color: #e57373;
}

.statusPending {
  background-color: rgba(255, 193, 7, 0.2);
  color: #ffd54f;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 5px;
}

.pageButton {
  background-color: rgba(255, 255, 255, 0.1);
  border: none;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.pageButton:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.pageButton.active {
  background-color: #4a90e2;
}

/* Loading indicator */
.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.spinner {
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  border-top: 4px solid white;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
