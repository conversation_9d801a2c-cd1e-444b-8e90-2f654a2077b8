import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
// import { roomService } from '../services/apiService'; // Không sử dụng nữa
import { Room } from '../types/room';
import styles from '../styles/rooms.module.css';
import BookingModal from './BookingModal';
import { toast } from 'react-toastify';
import Image from 'next/image';
import { useAuth } from '../contexts/AuthContext';

const RoomList = () => {
  const router = useRouter();
  const { user, isAuthenticated } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [filter, setFilter] = useState('all'); // all, available, booked

  useEffect(() => {
    const fetchRooms = async () => {
      try {
        setLoading(true);

        // Sử dụng dữ liệu mẫu trực tiếp thay vì gọi API
        console.log('Using mock data for rooms');
        setRooms(getMockRooms());
        setError('');

        // Không gọi API nữa vì nó yêu cầu xác thực
        // const response = await roomService.getAllRooms();
        // if (response.success && Array.isArray(response.data)) {
        //   setRooms(response.data);
        // } else if (Array.isArray(response)) {
        //   setRooms(response);
        // } else {
        //   console.error('Unexpected data format:', response);
        //   setRooms(getMockRooms());
        // }
      } catch (error: any) {
        console.error('Error in RoomList:', error);
        // Luôn sử dụng dữ liệu mẫu khi có lỗi
        setRooms(getMockRooms());
        setError('');
      } finally {
        setLoading(false);
      }
    };

    // Hàm tạo dữ liệu mẫu
    const getMockRooms = (): Room[] => {
      return [
        {
          roomId: 1,
          roomNumber: '101',
          roomTypeId: 1,
          floor: 1,
          status: 'Available',
          cleaningStatus: 'Clean',
          roomTypeName: 'Standard',
          price: 500000,
          capacity: 2,
          description: 'Phòng tiêu chuẩn với đầy đủ tiện nghi cơ bản.',
          amenities: 'WiFi, TV, Điều hòa, Tủ lạnh',
          bedType: 'Single'
        },
        {
          roomId: 2,
          roomNumber: '102',
          roomTypeId: 1,
          floor: 1,
          status: 'Available',
          cleaningStatus: 'Clean',
          roomTypeName: 'Standard',
          price: 500000,
          capacity: 2,
          description: 'Phòng tiêu chuẩn với đầy đủ tiện nghi cơ bản.',
          amenities: 'WiFi, TV, Điều hòa, Tủ lạnh',
          bedType: 'Double'
        },
        {
          roomId: 3,
          roomNumber: '201',
          roomTypeId: 2,
          floor: 2,
          status: 'Available',
          cleaningStatus: 'Clean',
          roomTypeName: 'Deluxe',
          price: 800000,
          capacity: 3,
          description: 'Phòng sang trọng với không gian rộng rãi và view đẹp.',
          amenities: 'WiFi, TV, Điều hòa, Tủ lạnh, Minibar, Bồn tắm',
          bedType: 'King'
        },
        {
          roomId: 4,
          roomNumber: '202',
          roomTypeId: 2,
          floor: 2,
          status: 'Booked',
          cleaningStatus: 'Clean',
          roomTypeName: 'Deluxe',
          price: 800000,
          capacity: 3,
          description: 'Phòng sang trọng với không gian rộng rãi và view đẹp.',
          amenities: 'WiFi, TV, Điều hòa, Tủ lạnh, Minibar, Bồn tắm',
          bedType: 'Queen'
        },
        {
          roomId: 5,
          roomNumber: '301',
          roomTypeId: 3,
          floor: 3,
          status: 'Available',
          cleaningStatus: 'Clean',
          roomTypeName: 'Suite',
          price: 1200000,
          capacity: 4,
          description: 'Phòng hạng sang với không gian rộng lớn và đầy đủ tiện nghi cao cấp.',
          amenities: 'WiFi, TV, Điều hòa, Tủ lạnh, Minibar, Bồn tắm, Phòng khách riêng',
          bedType: 'King'
        },
        {
          roomId: 6,
          roomNumber: '302',
          roomTypeId: 3,
          floor: 3,
          status: 'Available',
          cleaningStatus: 'Clean',
          roomTypeName: 'Suite',
          price: 1200000,
          capacity: 4,
          description: 'Phòng hạng sang với không gian rộng lớn và đầy đủ tiện nghi cao cấp.',
          amenities: 'WiFi, TV, Điều hòa, Tủ lạnh, Minibar, Bồn tắm, Phòng khách riêng',
          bedType: 'King'
        }
      ];
    };

    fetchRooms();
  }, []);

  const handleBooking = (room: Room) => {
    // Kiểm tra xem người dùng đã đăng nhập chưa
    if (!isAuthenticated) {
      toast.info('Vui lòng đăng nhập để đặt phòng');
      // Lưu thông tin phòng vào localStorage để sau khi đăng nhập có thể quay lại
      localStorage.setItem('selectedRoom', JSON.stringify(room));
      router.push('/auth/login?redirect=/room/room');
      return;
    }

    setSelectedRoom(room);
    setIsModalOpen(true);
  };

  const filteredRooms = rooms.filter(room => {
    if (filter === 'available') return room.status === 'Available';
    if (filter === 'booked') return room.status === 'Booked';
    return true;
  });

  if (loading) return <div className={styles.loading}>Loading rooms...</div>;
  if (error) return <div className={styles.error}>{error}</div>;

  return (
    <div className={styles.roomsContainer}>
      <div className={styles.filterTabs}>
        <button
          className={`${styles.filterTab} ${filter === 'all' ? styles.active : ''}`}
          onClick={() => setFilter('all')}
        >
          Tất cả phòng
        </button>
        <button
          className={`${styles.filterTab} ${filter === 'available' ? styles.active : ''}`}
          onClick={() => setFilter('available')}
        >
          Phòng có sẵn
        </button>
        <button
          className={`${styles.filterTab} ${filter === 'booked' ? styles.active : ''}`}
          onClick={() => setFilter('booked')}
        >
          Phòng đã đặt
        </button>
      </div>

      <div className={styles.roomsGrid}>
        {filteredRooms.map((room) => (
          <div key={room.roomId} className={styles.roomCard}>
            <div className={styles.roomImageContainer}>
              <Image
                src={`/rooms/${(room.roomTypeName || 'standard').toLowerCase()}.jpg`}
                alt={room.roomTypeName || 'Room'}
                layout="fill"
                objectFit="cover"
                className={styles.roomImage}
              />
              <div className={styles.roomStatus}>
                <span className={`${styles.statusBadge} ${styles[(room.status || 'available').toLowerCase()]}`}>
                  {room.status || 'Available'}
                </span>
              </div>
            </div>

            <div className={styles.roomContent}>
              <h3 className={styles.roomName}>
                {room.roomTypeName || 'Standard'} - Room {room.roomNumber}
              </h3>

              <div className={styles.roomFeatures}>
                <span className={styles.feature}>Floor {room.floor || '1'}</span>
                <span className={styles.feature}>{room.bedType || 'Single'}</span>
                <span className={styles.feature}>{room.capacity || '2'} Guests</span>
              </div>

              <div className={styles.roomDescription}>
                <p>{room.description || 'Comfortable room with all basic amenities'}</p>
              </div>

              <div className={styles.amenities}>
                {room.amenities ? room.amenities.split(',').map((amenity, index) => (
                  <span key={index} className={styles.amenity}>{amenity.trim()}</span>
                )) : (
                  <span className={styles.amenity}>Basic Amenities</span>
                )}
              </div>

              <div className={styles.roomFooter}>
                <div className={styles.priceInfo}>
                  <span className={styles.priceLabel}>Per Night</span>
                  <span className={styles.priceValue}>{room.price ? `${room.price.toLocaleString()} VND` : '500,000 VND'}</span>
                </div>
                <div className={styles.roomActions}>
                  <button
                    className={styles.detailButton}
                    onClick={() => router.push(`/room/${room.roomId}`)}
                  >
                    Chi tiết
                  </button>
                  {(room.status === 'Available' || !room.status) && (
                    <button
                      className={styles.bookButton}
                      onClick={() => handleBooking(room)}
                    >
                      Đặt phòng
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedRoom && (
        <BookingModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          room={{
            name: `${selectedRoom.roomTypeName || 'Standard'} - Room ${selectedRoom.roomNumber}`,
            price: `${selectedRoom.price ? selectedRoom.price.toLocaleString() : '500,000'} VND/night`,
            image: `/rooms/${(selectedRoom.roomTypeName || 'standard').toLowerCase()}.jpg`,
            roomId: selectedRoom.roomId,
            roomTypeId: selectedRoom.roomTypeId || 1
          }}
        />
      )}
    </div>
  );
};

export default RoomList;
