import { useState } from "react";
import Link from "next/link";
import Navbar from '../../components/Navbar';
import styles from "../../styles/auth.module.css";
import { validateEmail, validatePassword, validateUsername } from "../../utils/validation";
import { authService } from '../../services/authService';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';

export default function Register() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    userName: '',
    email: '',
    password: '',
    phone: '',
    firstName: '',
    lastName: ''
  });
  const [errors, setErrors] = useState({
    userName: '',
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    setErrors(prev => ({
      ...prev,
      [name]: ''
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate
    const userNameError = validateUsername(formData.userName);
    const emailError = validateEmail(formData.email);
    const passwordError = validatePassword(formData.password);

    if (userNameError || emailError || passwordError) {
      setErrors({
        ...errors,
        userName: userNameError,
        email: emailError,
        password: passwordError,
      });
      return;
    }

    try {
      await authService.register({
        userName: formData.userName,
        email: formData.email,
        password: formData.password,
        phone: formData.phone,
        firstName: formData.firstName,
        lastName: formData.lastName
      });

      toast.success('Đăng ký thành công!');
      router.push('/auth/login');
    } catch (error) {
      toast.error('Đăng ký thất bại. Vui lòng thử lại!');
    }
  };

  return (
    <>
      <Navbar />
      <div className={styles.authContainer}>
        <div className={styles.authCard}>
          <h2 className={styles.authTitle}>Đăng ký</h2>
          <form className={styles.authForm} onSubmit={handleSubmit}>
            <div className={styles.formGroup}>
              <label htmlFor="userName">Tên đăng nhập *</label>
              <input
                type="text"
                id="userName"
                name="userName"
                value={formData.userName}
                onChange={handleChange}
                placeholder="Nhập tên đăng nhập"
                required
              />
              {errors.userName && <span className={styles.errorText}>{errors.userName}</span>}
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="email">Email *</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Nhập email"
                required
              />
              {errors.email && <span className={styles.errorText}>{errors.email}</span>}
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="password">Mật khẩu *</label>
              <input
                type="password"
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="Nhập mật khẩu"
                required
              />
              {errors.password && <span className={styles.errorText}>{errors.password}</span>}
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="firstName">Tên</label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                placeholder="Nhập tên"
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="lastName">Họ</label>
              <input
                type="text"
                id="lastName"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                placeholder="Nhập họ"
              />
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="phone">Số điện thoại</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="Nhập số điện thoại"
              />
            </div>
            <button type="submit" className={styles.authButton}>
              Đăng ký
            </button>
          </form>
          <p className={styles.authSwitch}>
            Đã có tài khoản?{" "}
            <Link href="/auth/login" className={styles.authLink}>
              Đăng nhập
            </Link>
          </p>
        </div>
      </div>
    </>
  );
}