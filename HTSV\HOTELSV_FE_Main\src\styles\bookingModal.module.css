.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  backdrop-filter: blur(5px);
}

.modal {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  width: 90%;
  max-width: 500px;
  position: relative;
  animation: modalFadeIn 0.3s ease;
  backdrop-filter: blur(10px);
}

.closeButton {
  position: absolute;
  right: 1rem;
  top: 1rem;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.closeButton:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modalContent {
  margin: 0 auto;
  max-width: 400px;
}

.roomInfo {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  justify-content: center;
}

.roomImage {
  width: 120px;
  height: 80px;
  object-fit: cover;
  border-radius: 10px;
  margin-right: 1rem;
}

.roomDetails h3 {
  color: white;
  margin: 0;
}

.price {
  color: var(--primary-color);
  margin-top: 0.5rem;
}

.bookingForm {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.formGroup.full-width {
  grid-column: 1 / -1;
}

.formGroup label {
  color: white;
  font-size: 0.9rem;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 0.8rem;
  color: white;
  font-size: 0.9rem;
}

.formGroup select option {
  background-color: #121212;
  color: white;
}

.formGroup textarea {
  resize: vertical;
  min-height: 100px;
}

.submitButton {
  grid-column: 1 / -1;
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submitButton:hover {
  background: rgba(192, 160, 128, 0.8);
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}