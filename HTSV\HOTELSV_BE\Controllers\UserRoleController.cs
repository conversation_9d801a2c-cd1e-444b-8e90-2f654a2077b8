using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using HOTELSV_BE.Models;
using System.Data.SqlClient;
using Dapper;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserRoleController : ControllerBase
    {
        private readonly string _connectionString;

        public UserRoleController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ??
                throw new ArgumentNullException(nameof(configuration), "DefaultConnection string is not configured");
        }

        [HttpPost("Add")]
        [RequirePermission("create_userrole")]
        public async Task<ActionResult<AddUserRoleResponse>> AddUserRole([FromBody] AddUserRoleRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new
                    {
                        UserId = request.UserId,
                        RoleId = request.RoleId
                    };

                    var id = await connection.QueryFirstOrDefaultAsync<int>(
                        "sp_AddUserRole",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new AddUserRoleResponse
                    {
                        Success = true,
                        Message = "User role mapping added successfully",
                        UserRoleId = id
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new AddUserRoleResponse
                {
                    Success = false,
                    Message = $"Error adding user role: {ex.Message}"
                });
            }
        }

        [HttpDelete("{id}")]
        [RequirePermission("cancel_userrole")]
        public async Task<ActionResult<DeleteUserRoleResponse>> DeleteUserRole(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    await connection.ExecuteAsync(
                        "sp_DeleteUserRole",
                        new { UserRoleId = id },
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new DeleteUserRoleResponse
                    {
                        Success = true,
                        Message = "User role mapping deleted successfully"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new DeleteUserRoleResponse
                {
                    Success = false,
                    Message = $"Error deleting user role: {ex.Message}"
                });
            }
        }

        [HttpPut("Update")]
        [RequirePermission("edit_userrole")]
        public async Task<ActionResult<UpdateUserRoleResponse>> UpdateUserRole([FromBody] UpdateUserRoleRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new
                    {
                        UserRoleId = request.UserRoleId,
                        UserId = request.UserId,
                        RoleId = request.RoleId
                    };

                    var updated = await connection.QueryFirstOrDefaultAsync<UserRole>(
                        "sp_UpdateUserRole",
                        parameters,
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (updated == null)
                    {
                        return NotFound(new UpdateUserRoleResponse
                        {
                            Success = false,
                            Message = "User role mapping not found"
                        });
                    }

                    return Ok(new UpdateUserRoleResponse
                    {
                        Success = true,
                        Message = "User role mapping updated successfully",
                        UserRole = updated
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new UpdateUserRoleResponse
                {
                    Success = false,
                    Message = $"Error updating user role: {ex.Message}"
                });
            }
        }

        [HttpGet]
        [RequirePermission("view_userroles")]
        public async Task<ActionResult<GetAllUserRolesResponse>> GetAllUserRoles()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var userRoles = await connection.QueryAsync<UserRole>(
                        "sp_GetAllUserRoles",
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    return Ok(new GetAllUserRolesResponse
                    {
                        Success = true,
                        Message = "User roles retrieved successfully",
                        UserRoles = userRoles.ToList()
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetAllUserRolesResponse
                {
                    Success = false,
                    Message = $"Error retrieving user roles: {ex.Message}"
                });
            }
        }

        [HttpGet("{id}")]
        [RequirePermission("view_userroles")]
        public async Task<ActionResult<GetUserRoleResponse>> GetUserRoleById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var userRole = await connection.QueryFirstOrDefaultAsync<UserRole>(
                        "sp_GetUserRoleById",
                        new { UserRoleId = id },
                        commandType: System.Data.CommandType.StoredProcedure
                    );

                    if (userRole == null)
                    {
                        return NotFound(new GetUserRoleResponse
                        {
                            Success = false,
                            Message = "User role mapping not found"
                        });
                    }

                    return Ok(new GetUserRoleResponse
                    {
                        Success = true,
                        Message = "User role mapping retrieved successfully",
                        UserRole = userRole
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new GetUserRoleResponse
                {
                    Success = false,
                    Message = $"Error retrieving user role: {ex.Message}"
                });
            }
        }
    }
}
