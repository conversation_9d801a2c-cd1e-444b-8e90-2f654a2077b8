import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import styles from '../styles/rooms.module.css';
import { useAuth } from '../contexts/AuthContext';

const Navbar = () => {
  const [scrolled, setScrolled] = useState(false);
  const router = useRouter();
  const { isAuthenticated, logout, user } = useAuth();
  console.log("User info:", user);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  return (
    <nav className={`${styles.navbar} ${scrolled ? styles.scrolled : ""}`}>
      <div className={styles.container}>
        {/* Logo canh trái */}
        <div className={styles.navbarLeft}>
          <Link href="/" className={styles.navbarBrand}>
            <img src="/logo/logo.png" alt="Logo" className={styles.navLogo} />
            <span className={styles.brandText}>Hotel Nhóm 1</span>
          </Link>
        </div>

        {/* Menu chính canh giữa */}
        <div className={styles.navLinksContainer}>
          <div className={styles.mainMenu}>
            <Link href="/" className={`${styles.navLink} ${router.pathname === '/' ? styles.active : ''}`}>
              Trang chủ
            </Link>
            <Link href="/room/room" className={`${styles.navLink} ${router.pathname.includes('/room') ? styles.active : ''}`}>
              Phòng
            </Link>
            <Link href="/services" className={`${styles.navLink} ${router.pathname.includes('/services') ? styles.active : ''}`}>
              Dịch vụ
            </Link>
            <Link href="/reviews" className={`${styles.navLink} ${router.pathname === '/reviews' ? styles.active : ''}`}>
              Đánh giá
            </Link>
            <Link href="/about" className={`${styles.navLink} ${router.pathname === '/about' ? styles.active : ''}`}>
              Về chúng tôi
            </Link>
            <Link href="/contact" className={`${styles.navLink} ${router.pathname === '/contact' ? styles.active : ''}`}>
              Liên hệ
            </Link>
          </div>
        </div>

        {/* Các nút canh phải */}
        <div className={styles.navActions}>
          {isAuthenticated ? (
            <>
             <span className={styles.userName}>Xin chào</span>
            <Link href="/booking" className={styles.bookingsButton}>
              Đặt phòng của tôi
            </Link>
            {user?.roles?.includes("Administrator") && (
              <Link href="/admin" className={styles.adminButton}>
                Admin
              </Link>
            )}
            {user?.roles?.includes("Manager") && (
              <Link href="/admin" className={styles.adminButton}>
                Manager
              </Link>
)}
            {user?.roles?.includes("Receptionist") && (
              <Link href="/admin" className={styles.adminButton}>
                Receptionist
              </Link>
            )}
            {user?.roles?.includes("Housekeeper") && (
              <Link href="/admin" className={styles.adminButton}>
                Housekeeper
              </Link>
            )}
            {user?.roles?.includes("Maintenance") && (
              <Link href="/admin" className={styles.adminButton}>
                Maintenance
              </Link>
            )}
            <button onClick={handleLogout} className={styles.logoutButton}>
              Đăng xuất
            </button>
            </>
          ) : (
            <>
              <Link href="/auth/login" className={styles.loginButton}>
                Đăng nhập
              </Link>
              <Link href="/auth/register" className={styles.registerButton}>
                Đăng ký
              </Link>
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;