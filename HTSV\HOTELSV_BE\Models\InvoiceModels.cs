namespace HOTELSV_BE.Models
{
    public class InvoiceModels
    {
        public int InvoiceId { get; set; }
        public int BookingId { get; set; }
        public required string InvoiceNumber { get; set; }
        public DateTime InvoiceDate { get; set; }
        public DateTime DueDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal GrandTotal { get; set; }
        public required string Status { get; set; }
        public required string Notes { get; set; }
        public required string IssuedBy { get; set; }
    }

    public class AddInvoiceModels
    {
        public int BookingId { get; set; }
        public required string InvoiceNumber { get; set; }
        public DateTime DueDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public required string Notes { get; set; }
        public required string IssuedBy { get; set; }
    }

    public class UpdateInvoiceModels
    {
        public int BookingId { get; set; }
        public required string InvoiceNumber { get; set; }
        public DateTime DueDate { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal DiscountAmount { get; set; }
        public required string Status { get; set; }
        public required string Notes { get; set; }
        public required string IssuedBy { get; set; }
        public int TotalRecords { get; set; }
    }

    public class InvoiceFilterRequest
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public string? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SortBy { get; set; }
        public bool IsAscending { get; set; } = true;
    }
}