.aboutContainer {
  min-height: 100vh;
  background: #121212;
  color: #fff;
}

.heroSection {
  height: 60vh;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-top: 80px;
  overflow: hidden;
}

.heroSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('/hinhanhtrangchu/anhbackgroundtrangchu.png') center/cover;
  filter: brightness(0.3);
  z-index: -1;
}

.heroContent {
  position: relative;
  z-index: 1;
  animation: fadeInUp 1s ease;
}

.heroContent h1 {
  font-size: 4rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.aboutHeader {
  text-align: center;
  padding: 3rem 0;
  background: rgba(255, 255, 255, 0.05);
}

.aboutHeader h1 {
  color: var(--primary-color);
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.aboutHeader p {
  color: #fff;
}

.aboutSection {
  max-width: 1200px;
  margin: 0 auto;
  padding: 4rem 1rem;
}

.aboutContent {
  display: flex;
  flex-direction: column;
  gap: 4rem;
}

.missionVision {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 3rem;
  max-width: 1200px;
  margin: -50px auto 0;
  padding: 0 2rem;
  position: relative;
}

.missionBox,
.visionBox {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.missionBox:hover,
.visionBox:hover {
  transform: translateY(-5px);
  background: rgba(255, 255, 255, 0.08);
}

.missionBox h2,
.visionBox h2 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.8rem;
}

.missionBox p,
.visionBox p {
  color: #fff;
  line-height: 1.6;
}

.valuesSection {
  text-align: center;
}

.valuesSection h2 {
  color: var(--primary-color);
  margin-bottom: 2rem;
  font-size: 2rem;
}

.valuesList {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.valueItem {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  padding: 2rem;
  backdrop-filter: blur(10px);
}

.valueItem h3 {
  color: #fff;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.valueItem p {
  color: #fff;
  line-height: 1.6;
}

.contentSection {
  padding-top: 180px; /* Tăng padding-top để tránh bị navbar che */
  max-width: 1200px;
  margin: 0 auto;
  padding-left: 2rem;
  padding-right: 2rem;
}

.contentSection h1 {
  font-size: 2.5rem;
  color: #C0A080;
  margin-bottom: 1rem;
  text-align: center;
}

.contentSection p {
  text-align: center;
  color: #E0E0E0;
  margin-bottom: 2rem;
}

@media (max-width: 768px) {
  .missionVision {
    grid-template-columns: 1fr;
  }
  
  .valuesList {
    grid-template-columns: 1fr;
  }
}
