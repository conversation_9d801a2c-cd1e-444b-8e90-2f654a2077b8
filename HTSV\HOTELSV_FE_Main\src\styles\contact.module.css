.contactContainer {
  padding-top: 80px;
  min-height: 100vh;
  background: linear-gradient(to bottom, var(--background-color), #0f0f0f);
}

.contactHeader {
  text-align: center;
  padding: 4rem 0;
  background: rgba(255, 255, 255, 0.03);
  position: relative;
}

.contactHeader h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  background: linear-gradient(45deg, var(--primary-color), #D4AF37);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: fadeInDown 0.8s ease;
}

.contactHeader p {
  color: #fff;
  font-size: 1.1rem;
}

.mapContainer {
  height: 400px;
  width: 100%;
  margin-bottom: 4rem;
  border-radius: 20px;
  overflow: hidden;
}

.contactContent {
  max-width: 1200px;
  margin: -100px auto 4rem;
  padding: 2rem;
  position: relative;
  z-index: 2;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 3rem;
}

.contactInfo {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 2.5rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  height: fit-content;
}

.infoItem {
  margin-bottom: 2.5rem;
  padding-left: 3rem;
  position: relative;
}

.infoItem::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 2rem;
  height: 2rem;
  background-size: contain;
  background-repeat: no-repeat;
}

.infoItem h3 {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.infoItem p {
  color: #fff;
  line-height: 1.6;
}

.contactForm {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 20px;
  padding: 3rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  margin-bottom: 0.5rem;
  color: #fff;
  font-size: 1rem;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  width: 100%;
  background: rgba(255, 255, 255, 0.07);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 1rem;
  color: #fff;
  transition: all 0.3s ease;
}

.formGroup select option {
  background-color: #121212;
  color: white;
}

.formGroup input::placeholder,
.formGroup textarea::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.formGroup textarea {
  resize: vertical;
  min-height: 120px;
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  background: rgba(255, 255, 255, 0.1);
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(192, 160, 128, 0.1);
}

.submitButton {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.submitButton:hover {
  background: rgba(192, 160, 128, 0.8);
}

@media (max-width: 768px) {
  .contactContent {
    grid-template-columns: 1fr;
  }
}
