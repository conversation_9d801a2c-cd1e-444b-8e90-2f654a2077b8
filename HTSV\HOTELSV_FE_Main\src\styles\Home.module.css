/* Global Styles */
body {
  margin: 0;
  padding: 0;
  background: #121212;
  color: #fff;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: -15px;
}

.colMd6 {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 15px;
}

@media (max-width: 768px) {
  .colMd6 {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

.textPurple {
  color: #C0A080 !important;
}

/* Hero Section */
.heroSection {
  position: relative;
  width: 100%;
  height: 600px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-align: center;
  margin-top: 0;
  padding-top: 60px; /* Adjust for fixed navbar */
  overflow: hidden;
}

.heroContent {
  position: relative;
  z-index: 2;
  text-align: center;
}

.heroTitle {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.9), 
               -3px -3px 6px rgba(0, 0, 0, 0.9);
}

.btnPrimary {
  display: inline-block;
  padding: 15px 40px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  border-radius: 30px;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  margin-top: 25px;
}

.btnPrimary:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* Intro Section */
.introSection {
  padding: 140px 0 80px; /* Tăng padding-top để chữ "Về chúng tôi" không bị che */
  background-color: #0a0a0a;
}

.introSection p {
  color: #E0E0E0;
}

.sectionTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #E0E0E0;
  margin-bottom: 30px;
  padding-top: 20px;
}

.btnSecondary {
  display: inline-block;
  padding: 10px 20px;
  background-color: #6c757d;
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: 500;
  transition: background-color 0.3s;
  margin-top: 20px;
}

.btnSecondary:hover {
  background-color: #5a6268;
}

.introImageContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.introImage {
  border-radius: 8px;
  width: 100%;
  height: auto;
}

/* Gallery Section */
.gallerySection {
  position: relative;
  padding: 80px 0;
  text-align: center;
  background-image: url('/hinhanhtrangchu/anhbackgroundphongtrungbay.png');
  background-repeat: no-repeat;
  background-position: center center;
  background-size: cover;
  color: white;
}

.galleryOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.galleryContainer {
  position: relative;
  z-index: 2;
  max-width: 1140px;
  margin: 0 auto;
  padding: 0 15px;
}

.galleryTitle {
  font-weight: 700;
  margin-bottom: 30px;
}

.galleryRow {
  display: flex;
  flex-wrap: wrap;
  margin: -15px;
}

.galleryCol {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 15px;
}

.galleryImg {
  width: 100%;
  height: auto;
  border-radius: 8px;
  border: 5px solid #1a1a1a;
}

@media (max-width: 768px) {
  .galleryCol {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

@media (max-width: 576px) {
  .galleryCol {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Why Us Section */
.whyUsSection {
  padding: 80px 0;
  text-align: center;
  background-color: #121212;
}

.whyUsTitle {
  color: #C0A080;
}

.whyUsSection p {
  color: #E0E0E0;
}

.whyUsRow {
  display: flex;
  flex-wrap: wrap;
  margin: -15px;
  margin-top: 30px;
}

.whyUsCol {
  flex: 0 0 50%;
  max-width: 50%;
  padding: 15px;
}

.iconContainer {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a1a1a;
  border: 1px solid #2a2a2a;
  margin: 0 auto 20px;
  overflow: hidden;
}

.iconImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

@media (max-width: 768px) {
  .whyUsCol {
    flex: 0 0 100%;
    max-width: 100%;
  }
}

/* Contact Section */
.contactSection {
  margin-top: -60px; /* Di chuyển section lên trên */
  padding: 100px 0 80px;
  background-color: #0a0a0a;
  position: relative;
}

.contactTitle {
  font-size: 2.5rem;
  font-weight: 700;
  color: #E0E0E0;
  margin-bottom: 40px;
  text-align: center;
  margin-top: -20px; /* Điều chỉnh tiêu đề lên trên */
}

/* Footer */
.footer {
  background: rgba(18, 18, 18, 1);
  color: #E0E0E0;
  padding: 40px 0;
}

.footerRow {
  display: flex;
  justify-content: flex-end; /* Căn phải toàn bộ nội dung */
  flex-wrap: wrap;
  margin: -15px;
}

.footerCol {
  flex: 0 0 33.333333%;
  max-width: 33.333333%;
  padding: 15px;
  padding-left: 80px; /* Thêm padding bên trái */
}

.footerTitle {
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 20px;
  font-size: 1.3rem;
  text-align: left; /* Đảm bảo căn trái */
}

.footerCol p {
  color: #999999;
  margin-bottom: 10px;
  font-size: 1rem;
  line-height: 1.5;
}

.footerCol p strong {
  color: #E0E0E0;
  font-weight: 600;
}

.footerList {
  list-style: none;
  padding: 0;
  margin: 0;
  text-align: left; /* Đảm bảo căn trái */
}

.footerList li {
  margin-bottom: 10px;
}

.footerLink {
  color: #999999;
  text-decoration: none;
  transition: color 0.3s;
  font-size: 1rem;
  display: inline-block;
  margin-bottom: 8px;
}

.footerLink:hover {
  color: var(--primary-color);
}

@media (max-width: 768px) {
  .footerCol {
    flex: 0 0 50%;
    max-width: 50%;
    padding-left: 15px;
  }
}

@media (max-width: 576px) {
  .footerCol {
    flex: 0 0 100%;
    max-width: 100%;
  }
}