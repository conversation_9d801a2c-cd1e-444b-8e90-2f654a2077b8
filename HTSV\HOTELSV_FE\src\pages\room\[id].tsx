import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
// import { roomService } from '../../services/apiService'; // Không sử dụng nữa
import Navbar from '../../components/Navbar';
import styles from '../../styles/rooms.module.css';
import BookingModal from '../../components/BookingModal';
import { toast } from 'react-toastify';
import Image from 'next/image';
import { useAuth } from '../../contexts/AuthContext';
import Head from 'next/head';

interface Room {
  roomId: number;
  roomNumber: string;
  roomTypeId: number;
  floor: number;
  status: string;
  cleaningStatus: string;
  roomTypeName?: string;
  price?: number;
  capacity?: number;
  description?: string;
  amenities?: string;
  bedType?: string;
}

export default function RoomDetail() {
  const router = useRouter();
  const { id } = router.query;
  const { isAuthenticated } = useAuth();

  const [room, setRoom] = useState<Room | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);

  useEffect(() => {
    const fetchRoomDetail = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Sử dụng dữ liệu mẫu trực tiếp thay vì gọi API
        console.log('Using mock data for room details');
        setRoom(getMockRoom(Number(id)));
        setError('');

        // Không gọi API nữa vì nó yêu cầu xác thực
        // const response = await roomService.getRoomById(Number(id));
        // if (response && response.success && response.data) {
        //   setRoom(response.data);
        // } else {
        //   throw new Error('Không thể tải thông tin phòng');
        // }
      } catch (error: any) {
        console.error('Error in RoomDetail:', error);
        // Luôn sử dụng dữ liệu mẫu khi có lỗi
        setRoom(getMockRoom(Number(id)));
        setError('');
      } finally {
        setLoading(false);
      }
    };

    // Hàm tạo dữ liệu mẫu cho phòng
    const getMockRoom = (roomId: number): Room => {
      // Danh sách các loại phòng mẫu
      const roomTypes = [
        { id: 1, name: 'Standard', price: 500000, capacity: 2, bedType: 'Single' },
        { id: 2, name: 'Deluxe', price: 800000, capacity: 3, bedType: 'Queen' },
        { id: 3, name: 'Suite', price: 1200000, capacity: 4, bedType: 'King' }
      ];

      // Chọn loại phòng dựa trên ID
      const roomTypeIndex = (roomId % 3);
      const roomType = roomTypes[roomTypeIndex === 0 ? 2 : roomTypeIndex - 1];

      // Tạo số phòng dựa trên ID
      const floor = Math.floor(roomId / 100) + 1;
      const roomNumber = `${floor}${String(roomId).padStart(2, '0')}`;

      return {
        roomId: roomId,
        roomNumber: roomNumber,
        roomTypeId: roomType.id,
        floor: floor,
        status: 'Available',
        cleaningStatus: 'Clean',
        roomTypeName: roomType.name,
        price: roomType.price,
        capacity: roomType.capacity,
        description: roomType.name === 'Standard'
          ? 'Phòng tiêu chuẩn với đầy đủ tiện nghi cơ bản.'
          : roomType.name === 'Deluxe'
            ? 'Phòng sang trọng với không gian rộng rãi và view đẹp.'
            : 'Phòng hạng sang với không gian rộng lớn và đầy đủ tiện nghi cao cấp.',
        amenities: roomType.name === 'Standard'
          ? 'WiFi, TV, Điều hòa, Tủ lạnh'
          : roomType.name === 'Deluxe'
            ? 'WiFi, TV, Điều hòa, Tủ lạnh, Minibar, Bồn tắm'
            : 'WiFi, TV, Điều hòa, Tủ lạnh, Minibar, Bồn tắm, Phòng khách riêng',
        bedType: roomType.bedType
      };
    };

    fetchRoomDetail();
  }, [id]);

  const handleBooking = () => {
    if (!isAuthenticated) {
      toast.info('Vui lòng đăng nhập để đặt phòng');
      // Lưu thông tin phòng vào localStorage để sau khi đăng nhập có thể quay lại
      if (room) {
        localStorage.setItem('selectedRoom', JSON.stringify(room));
      }
      router.push(`/auth/login?redirect=/room/${id}`);
      return;
    }

    setIsModalOpen(true);
  };

  if (loading) return (
    <>
      <Navbar />
      <div className={styles.container}>
        <div className={styles.loading}>Đang tải thông tin phòng...</div>
      </div>
    </>
  );

  if (error && !room) return (
    <>
      <Navbar />
      <div className={styles.container}>
        <div className={styles.error}>{error}</div>
        <button
          className={styles.backButton}
          onClick={() => router.push('/room/room')}
        >
          Quay lại danh sách phòng
        </button>
      </div>
    </>
  );

  if (!room) return (
    <>
      <Navbar />
      <div className={styles.container}>
        <div className={styles.error}>Không tìm thấy thông tin phòng</div>
        <button
          className={styles.backButton}
          onClick={() => router.push('/room/room')}
        >
          Quay lại danh sách phòng
        </button>
      </div>
    </>
  );

  return (
    <>
      <Head>
        <title>{`${room.roomTypeName || 'Phòng'} ${room.roomNumber} | Hotel Nhóm 1`}</title>
      </Head>
      <Navbar />
      <div className={styles.roomDetailContainer}>
        <div className={styles.roomDetailHeader}>
          <button
            className={styles.backButton}
            onClick={() => router.push('/room/room')}
          >
            &larr; Quay lại
          </button>
          <h1>{room.roomTypeName || 'Phòng'} {room.roomNumber}</h1>
        </div>

        <div className={styles.roomDetailContent}>
          <div className={styles.roomDetailImageContainer}>
            <Image
              src={`/rooms/${(room.roomTypeName || 'standard').toLowerCase()}.jpg`}
              alt={room.roomTypeName || 'Room'}
              width={600}
              height={400}
              layout="responsive"
              className={styles.roomDetailImage}
            />
            <div className={styles.roomStatus}>
              <span className={`${styles.statusBadge} ${styles[(room.status || 'available').toLowerCase()]}`}>
                {room.status || 'Available'}
              </span>
            </div>
          </div>

          <div className={styles.roomDetailInfo}>
            <div className={styles.roomDetailSection}>
              <h2>Thông tin phòng</h2>
              <div className={styles.roomDetailGrid}>
                <div className={styles.roomDetailItem}>
                  <span className={styles.roomDetailLabel}>Loại phòng:</span>
                  <span className={styles.roomDetailValue}>{room.roomTypeName || 'Standard'}</span>
                </div>
                <div className={styles.roomDetailItem}>
                  <span className={styles.roomDetailLabel}>Số phòng:</span>
                  <span className={styles.roomDetailValue}>{room.roomNumber}</span>
                </div>
                <div className={styles.roomDetailItem}>
                  <span className={styles.roomDetailLabel}>Tầng:</span>
                  <span className={styles.roomDetailValue}>{room.floor}</span>
                </div>
                <div className={styles.roomDetailItem}>
                  <span className={styles.roomDetailLabel}>Trạng thái:</span>
                  <span className={styles.roomDetailValue}>{room.status || 'Available'}</span>
                </div>
                <div className={styles.roomDetailItem}>
                  <span className={styles.roomDetailLabel}>Sức chứa:</span>
                  <span className={styles.roomDetailValue}>{room.capacity || 2} người</span>
                </div>
                <div className={styles.roomDetailItem}>
                  <span className={styles.roomDetailLabel}>Loại giường:</span>
                  <span className={styles.roomDetailValue}>{room.bedType || 'Single'}</span>
                </div>
                <div className={styles.roomDetailItem}>
                  <span className={styles.roomDetailLabel}>Giá phòng:</span>
                  <span className={styles.roomDetailValue}>{room.price ? `${room.price.toLocaleString()} VND/đêm` : '500,000 VND/đêm'}</span>
                </div>
              </div>
            </div>

            <div className={styles.roomDetailSection}>
              <h2>Mô tả</h2>
              <p className={styles.roomDescription}>
                {room.description || 'Phòng tiện nghi với đầy đủ các tiện ích cơ bản.'}
              </p>
            </div>

            <div className={styles.roomDetailSection}>
              <h2>Tiện nghi</h2>
              <div className={styles.amenitiesList}>
                {room.amenities ? room.amenities.split(',').map((amenity, index) => (
                  <span key={index} className={styles.amenityItem}>{amenity.trim()}</span>
                )) : (
                  <span className={styles.amenityItem}>Tiện nghi cơ bản</span>
                )}
              </div>
            </div>

            {(room.status === 'Available' || !room.status) && (
              <button
                className={styles.bookButton}
                onClick={handleBooking}
              >
                Đặt phòng ngay
              </button>
            )}
          </div>
        </div>
      </div>

      {room && (
        <BookingModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          room={{
            name: `${room.roomTypeName || 'Standard'} - Room ${room.roomNumber}`,
            price: `${room.price ? room.price.toLocaleString() : '500,000'} VND/night`,
            image: `/rooms/${(room.roomTypeName || 'standard').toLowerCase()}.jpg`,
            roomId: room.roomId,
            roomTypeId: room.roomTypeId || 1
          }}
        />
      )}
    </>
  );
}
