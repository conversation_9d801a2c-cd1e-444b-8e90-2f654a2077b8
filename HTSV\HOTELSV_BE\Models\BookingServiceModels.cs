using System;

namespace HOTELSV_BE.Models
{
    public class AddBookingServiceRequest
    {
        public int BookingId { get; set; }
        public int ServiceId { get; set; }
        public int Quantity { get; set; } = 1;
        public DateTime? ServiceDate { get; set; }
        public string Notes { get; set; }
        public int? RequestedBy { get; set; }
    }

    public class BookingServiceResponse
    {
        public int BookingServiceId { get; set; }
        public int BookingId { get; set; }
        public int ServiceId { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public DateTime ServiceDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public int? RequestedBy { get; set; }
        public int? ServedBy { get; set; }
    }
    public class UpdateBookingServiceRequest
    {
        public int BookingServiceId { get; set; }
        public int? BookingId { get; set; }
        public int? ServiceId { get; set; }
        public int? Quantity { get; set; }
        public decimal? Price { get; set; }
        public DateTime? ServiceDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public int? RequestedBy { get; set; }
        public int? ServedBy { get; set; }
    }

    public class UpdateBookingServiceResponse
    {
        public int BookingServiceId { get; set; }
        public int BookingId { get; set; }
        public int ServiceId { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public DateTime ServiceDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public int? RequestedBy { get; set; }
        public int? ServedBy { get; set; }
        public string ServiceName { get; set; }
        public string ServiceDescription { get; set; }
        public string RequestedByName { get; set; }
        public string ServedByName { get; set; }
    }
    public class BookingServiceListItem
    {
        public int BookingServiceId { get; set; }
        public int BookingId { get; set; }
        public int ServiceId { get; set; }
        public string ServiceName { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public DateTime ServiceDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public int? RequestedBy { get; set; }
        public int? ServedBy { get; set; }
    }
    public class BookingServiceDetail
    {
        public int BookingServiceId { get; set; }
        public int BookingId { get; set; }
        public int ServiceId { get; set; }
        public string ServiceName { get; set; }
        public int Quantity { get; set; }
        public decimal Price { get; set; }
        public DateTime ServiceDate { get; set; }
        public string Status { get; set; }
        public string Notes { get; set; }
        public int? RequestedBy { get; set; }
        public int? ServedBy { get; set; }
        public int TotalRecords { get; set; }
    }

    public class BookingServiceFilterRequest
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public int? BookingId { get; set; }
        public int? ServiceId { get; set; }
        public string? Status { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string? SortBy { get; set; }
        public bool IsAscending { get; set; } = true;
    }
}