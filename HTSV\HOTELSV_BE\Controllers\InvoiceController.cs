using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using HOTELSV_BE.Models;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class InvoiceController : ControllerBase
    {
        private readonly string _connectionString;

        public InvoiceController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection") ??
                throw new ArgumentNullException(nameof(configuration), "DefaultConnection string is not configured");
        }

        [HttpGet("GetAll")]
        [RequirePermission("view_invoices")]
        public async Task<IActionResult> GetAllInvoices()
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var invoices = await connection.QueryAsync<InvoiceModels>(
                    "SELECT * FROM Invoices"
                );
                return Ok(invoices);
            }
        }

        [HttpGet("GetById/{id}")]
        [RequirePermission("view_invoices")]
        public async Task<IActionResult> GetInvoiceById(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var invoice = await connection.QueryFirstOrDefaultAsync<InvoiceModels>(
                    "sp_GetInvoiceDetails",
                    new { InvoiceId = id },
                    commandType: CommandType.StoredProcedure
                );

                if (invoice == null)
                    return NotFound(new { message = "Không tìm thấy hóa đơn với ID này." });

                return Ok(invoice);
            }
        }

        [HttpPost("Add")]
        [RequirePermission("create_invoice")]
        public async Task<IActionResult> AddInvoice(AddInvoiceModels invoice)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingId", invoice.BookingId);
                    parameters.Add("@InvoiceNumber", invoice.InvoiceNumber);
                    parameters.Add("@DueDate", invoice.DueDate);
                    parameters.Add("@TotalAmount", invoice.TotalAmount);
                    parameters.Add("@TaxAmount", invoice.TaxAmount);
                    parameters.Add("@DiscountAmount", invoice.DiscountAmount);
                    parameters.Add("@Notes", invoice.Notes);
                    parameters.Add("@IssuedBy", invoice.IssuedBy);

                    var result = await connection.QueryFirstOrDefaultAsync<InvoiceModels>(
                        "sp_AddInvoice",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new {
                        success = true,
                        message = "Thêm hóa đơn thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi thêm hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("edit_invoice")]
        public async Task<IActionResult> UpdateInvoice(int id, UpdateInvoiceModels invoice)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@InvoiceId", id);
                    parameters.Add("@BookingId", invoice.BookingId);
                    parameters.Add("@InvoiceNumber", invoice.InvoiceNumber);
                    parameters.Add("@DueDate", invoice.DueDate);
                    parameters.Add("@TotalAmount", invoice.TotalAmount);
                    parameters.Add("@TaxAmount", invoice.TaxAmount);
                    parameters.Add("@DiscountAmount", invoice.DiscountAmount);
                    parameters.Add("@Status", invoice.Status);
                    parameters.Add("@Notes", invoice.Notes);
                    parameters.Add("@IssuedBy", invoice.IssuedBy);

                    var result = await connection.ExecuteAsync(
                        "sp_UpdateInvoice",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy hóa đơn với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Cập nhật hóa đơn thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi cập nhật hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("Delete/{id}")]
        [RequirePermission("cancel_invoice")]
        public async Task<IActionResult> DeleteInvoice(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    // First delete related invoice items
                    await connection.ExecuteAsync(
                        "DELETE FROM InvoiceItems WHERE InvoiceId = @Id",
                        new { Id = id }
                    );

                    // Then delete the invoice
                    var result = await connection.ExecuteAsync(
                        "DELETE FROM Invoices WHERE InvoiceId = @Id",
                        new { Id = id }
                    );

                    if (result == 0)
                    {
                        return NotFound(new {
                            success = false,
                            message = $"Không tìm thấy hóa đơn với ID: {id}"
                        });
                    }

                    return Ok(new {
                        success = true,
                        message = "Xóa hóa đơn thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi xóa hóa đơn",
                    error = ex.Message
                });
            }
        }

        [HttpGet]
        [RequirePermission("view_invoices")]
        public async Task<ActionResult<PaginatedResponse<InvoiceModels>>> GetInvoices([FromQuery] InvoiceFilterRequest filter)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@PageNumber", filter.PageNumber);
                    parameters.Add("@PageSize", filter.PageSize);
                    parameters.Add("@SearchTerm", filter.SearchTerm);
                    parameters.Add("@Status", filter.Status);
                    parameters.Add("@FromDate", filter.FromDate);
                    parameters.Add("@ToDate", filter.ToDate);
                    parameters.Add("@SortBy", filter.SortBy ?? "InvoiceDate");
                    parameters.Add("@IsAscending", filter.IsAscending);

                    var result = await connection.QueryAsync<InvoiceModels>(
                        "sp_GetInvoicesPaginated",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var invoices = result.ToList();
                    var totalItems = invoices.Any() ? invoices.First().TotalRecords : 0;

                    var paginatedResponse = new PaginatedResponse<InvoiceModels>(
                        invoices,
                        totalItems,
                        filter.PageNumber,
                        filter.PageSize
                    );

                    return Ok(new
                    {
                        success = true,
                        data = paginatedResponse
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách hóa đơn",
                    error = ex.Message
                });
            }
        }
    }
}