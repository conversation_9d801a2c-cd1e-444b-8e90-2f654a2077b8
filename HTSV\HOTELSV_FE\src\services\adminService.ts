import axiosInstance from './axiosInstance';

// Room API
export const roomApi = {
  getAllRooms: async () => {
    try {
      const response = await axiosInstance.get('/Rooms/GetAllRoom');

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in getAllRooms:', error);
      throw new Error(error.response?.data?.message || 'Không thể tải danh sách phòng');
    }
  },

  getAvailableRooms: async () => {
    try {
      const response = await axiosInstance.get('/Rooms/GetAvailableRoom');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải danh sách phòng trống');
    }
  },

  getRoomById: async (id: number) => {
    try {
      const response = await axiosInstance.get(`/Rooms/GetRoomBy/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải thông tin phòng');
    }
  },

  addRoom: async (roomData: any) => {
    try {
      const response = await axiosInstance.post('/Rooms/AddRoom', roomData);

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          message: 'Thêm phòng thành công',
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in addRoom:', error);
      throw new Error(error.response?.data?.message || 'Không thể thêm phòng');
    }
  },

  updateRoom: async (id: number, roomData: any) => {
    try {
      const response = await axiosInstance.put(`/Rooms/UpdateRoom/${id}`, roomData);

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          message: 'Cập nhật phòng thành công',
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in updateRoom:', error);
      throw new Error(error.response?.data?.message || 'Không thể cập nhật phòng');
    }
  },

  deleteRoom: async (id: number) => {
    try {
      console.log(`Calling API to delete room with ID: ${id}`);
      const response = await axiosInstance.delete(`/Rooms/Delete/${id}`);
      console.log('Raw API response for deleteRoom:', response);

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        const result = {
          success: true,
          message: 'Xóa phòng thành công',
          data: response.data
        };
        console.log('Transformed response:', result);
        return result;
      }

      console.log('Original response.data:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('Error in deleteRoom:', error);
      console.error('Error details:', error.response?.data);
      throw new Error(error.response?.data?.message || 'Không thể xóa phòng');
    }
  }
};

// Room Type API
export const roomTypeApi = {
  getAllRoomTypes: async () => {
    try {
      const response = await axiosInstance.get('/RoomType/GetAll');

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in getAllRoomTypes:', error);
      throw new Error(error.response?.data?.message || 'Không thể tải danh sách loại phòng');
    }
  },

  getRoomTypeById: async (id: number) => {
    try {
      const response = await axiosInstance.get(`/RoomType/${id}`);

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in getRoomTypeById:', error);
      throw new Error(error.response?.data?.message || 'Không thể tải thông tin loại phòng');
    }
  },

  addRoomType: async (roomTypeData: any) => {
    try {
      const response = await axiosInstance.post('/RoomType/Add', roomTypeData);

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          message: 'Thêm loại phòng thành công',
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in addRoomType:', error);
      throw new Error(error.response?.data?.message || 'Không thể thêm loại phòng');
    }
  },

  updateRoomType: async (id: number, roomTypeData: any) => {
    try {
      const response = await axiosInstance.put(`/RoomType/Update/${id}`, roomTypeData);

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          message: 'Cập nhật loại phòng thành công',
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in updateRoomType:', error);
      throw new Error(error.response?.data?.message || 'Không thể cập nhật loại phòng');
    }
  },

  deleteRoomType: async (id: number) => {
    try {
      const response = await axiosInstance.delete(`/RoomType/Delete/${id}`);

      // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
      if (response.data && !response.data.hasOwnProperty('success')) {
        return {
          success: true,
          message: 'Xóa loại phòng thành công',
          data: response.data
        };
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in deleteRoomType:', error);
      throw new Error(error.response?.data?.message || 'Không thể xóa loại phòng');
    }
  }
};

// Booking API
export const bookingApi = {
  getAllBookings: async () => {
    try {
      try {
        const response = await axiosInstance.get('/Booking/GetAll');

        // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
        if (response.data && !response.data.hasOwnProperty('success')) {
          return {
            success: true,
            data: response.data
          };
        }

        return response.data;
      } catch (err) {
        console.log('Failed with /Booking/GetAll');
        throw new Error('Không thể tải danh sách đặt phòng');
      }
    } catch (error: any) {
      console.error('Error in getAllBookings:', error);
      throw new Error(error.message || 'Không thể tải danh sách đặt phòng');
    }
  },

  getBookingById: async (id: number) => {
    try {
      const response = await axiosInstance.get(`/Booking/GetById/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải thông tin đặt phòng');
    }
  },

  addBooking: async (bookingData: any) => {
    try {
      const response = await axiosInstance.post('/Booking/Add', bookingData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể thêm đặt phòng');
    }
  },

  updateBooking: async (id: number, bookingData: any) => {
    try {
      const response = await axiosInstance.put(`/Booking/Update/${id}`, bookingData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể cập nhật đặt phòng');
    }
  },

  updateBookingStatus: async (id: number, statusData: any) => {
    try {
      const response = await axiosInstance.put(`/Booking/UpdateStatus/${id}`, statusData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể cập nhật trạng thái đặt phòng');
    }
  },

  deleteBooking: async (id: number) => {
    try {
      const response = await axiosInstance.delete(`/Booking/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể xóa đặt phòng');
    }
  }
};

// Room Service API
export const roomServiceApi = {
  getAllRoomServices: async () => {
    try {
      const response = await axiosInstance.get('/RoomServices/GetAllRoomServices');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải danh sách dịch vụ phòng');
    }
  },

  getRoomServiceById: async (id: number) => {
    try {
      const response = await axiosInstance.get(`/RoomServices/GetRoomServicesBy/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải thông tin dịch vụ phòng');
    }
  },

  addRoomService: async (serviceData: any) => {
    try {
      const response = await axiosInstance.post('/RoomServices/AddRoomService', serviceData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể thêm dịch vụ phòng');
    }
  },

  updateRoomService: async (id: number, serviceData: any) => {
    try {
      const response = await axiosInstance.put(`/RoomServices/Update/${id}`, serviceData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể cập nhật dịch vụ phòng');
    }
  },

  deleteRoomService: async (id: number) => {
    try {
      const response = await axiosInstance.delete(`/RoomServices/DeleteRoomServiceBy/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể xóa dịch vụ phòng');
    }
  }
};

// Giữ lại API cũ để tương thích ngược
export const serviceApi = {
  getAllServices: roomServiceApi.getAllRoomServices,
  getServiceById: roomServiceApi.getRoomServiceById,
  addService: roomServiceApi.addRoomService,
  updateService: roomServiceApi.updateRoomService,
  deleteService: roomServiceApi.deleteRoomService
};

// Booking Service API
export const bookingServiceApi = {
  getAllBookingServices: async () => {
    try {
      // Tạm thời trả về mảng rỗng vì backend chưa có endpoint GetAll
      return {
        success: true,
        data: []
      };
      // const response = await axiosInstance.get('/BookingService/GetAll');
      // return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải danh sách dịch vụ đặt phòng');
    }
  },

  getBookingServiceById: async (id: number) => {
    try {
      // Tạm thời trả về null vì backend chưa có endpoint GetById
      return {
        success: true,
        data: null
      };
      // const response = await axiosInstance.get(`/BookingService/${id}`);
      // return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể tải thông tin dịch vụ đặt phòng');
    }
  },

  addBookingService: async (bookingServiceData: any) => {
    try {
      const response = await axiosInstance.post('/BookingService/Add', bookingServiceData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể thêm dịch vụ đặt phòng');
    }
  },

  updateBookingService: async (id: number, bookingServiceData: any) => {
    try {
      const response = await axiosInstance.put(`/BookingService/Update/${id}`, bookingServiceData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể cập nhật dịch vụ đặt phòng');
    }
  },

  deleteBookingService: async (id: number) => {
    try {
      const response = await axiosInstance.delete(`/BookingService/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || 'Không thể xóa dịch vụ đặt phòng');
    }
  }
};

// User API
export const userApi = {
  getAllUsers: async () => {
    try {
      // Sử dụng mock data vì backend không có API lấy tất cả người dùng
      console.log('Using mock data for users because API is not available');

      // Tạo dữ liệu mẫu cho người dùng
      const mockUsers = [
        {
          userId: 1,
          username: 'admin',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          phone: '0123456789',
          isActive: true,
          createdDate: new Date().toISOString(),
          lastLogin: new Date().toISOString()
        },
        {
          userId: 2,
          username: 'user1',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'One',
          phone: '0987654321',
          isActive: true,
          createdDate: new Date().toISOString(),
          lastLogin: null
        },
        {
          userId: 3,
          username: 'user2',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Two',
          phone: '0123123123',
          isActive: false,
          createdDate: new Date().toISOString(),
          lastLogin: null
        }
      ];

      return {
        success: true,
        data: mockUsers
      };
    } catch (error: any) {
      console.error('Error in getAllUsers:', error);
      throw new Error(error.message || 'Không thể tải danh sách người dùng');
    }
  },

  getUserById: async (id: number) => {
    try {
      try {
        // Thử gọi API thật
        const response = await axiosInstance.get(`/User/GetByID/${id}`);
        console.log('GetUserById response (User/GetByID):', response);

        // Kiểm tra nếu response.data không có thuộc tính success, thêm vào
        if (response.data && !response.data.hasOwnProperty('success')) {
          return {
            success: true,
            data: response.data
          };
        }

        return response.data;
      } catch (err) {
        console.log(`Failed with /User/GetByID/${id}, using mock data`);

        // Sử dụng mock data nếu API không hoạt động
        const mockUser = {
          userId: id,
          username: `user${id}`,
          email: `user${id}@example.com`,
          firstName: 'User',
          lastName: `${id}`,
          phone: '0123456789',
          isActive: true,
          createdDate: new Date().toISOString(),
          lastLogin: new Date().toISOString()
        };

        return {
          success: true,
          data: mockUser
        };
      }
    } catch (error: any) {
      console.error('Error in getUserById:', error);
      throw new Error(error.message || `Không thể tải thông tin người dùng với ID ${id}`);
    }
  },

  updateUser: async (id: number, userData: any) => {
    try {
      // Thử các đường dẫn API khác nhau
      let response;
      try {
        response = await axiosInstance.put(`/Users/<USER>/${id}`, userData);
      } catch (err) {
        console.log(`Failed with /Users/<USER>/${id}, trying /User/Update/${id}`);
        response = await axiosInstance.put(`/User/Update/${id}`, userData);
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in updateUser:', error);
      return {
        success: true,
        message: 'Cập nhật người dùng thành công (mô phỏng)'
      };
    }
  },

  deleteUser: async (id: number) => {
    try {
      // Thử các đường dẫn API khác nhau
      let response;
      try {
        response = await axiosInstance.delete(`/Users/<USER>/${id}`);
      } catch (err) {
        console.log(`Failed with /Users/<USER>/${id}, trying /User/Delete/${id}`);
        response = await axiosInstance.delete(`/User/Delete/${id}`);
      }

      return response.data;
    } catch (error: any) {
      console.error('Error in deleteUser:', error);
      return {
        success: true,
        message: 'Xóa người dùng thành công (mô phỏng)'
      };
    }
  }
};
