using System;

namespace HOTELSV_BE.Models
{
    public class AddCustomerRequest
    {
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Phone { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
        public string IDNumber { get; set; }
        public string IDType { get; set; }
        public string Nationality { get; set; }
    }

    public class AddCustomerResponse
    {
        public int CustomerId { get; set; }
        public string ErrorMessage { get; set; }
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);
    }

    public class CustomerDetail
    {
        // User information
        public int UserId { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Phone { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }

        // Customer specific information 
        public int CustomerId { get; set; }
        public string Address { get; set; }
        public string City { get; set; }
        public string Country { get; set; }
        public string PostalCode { get; set; }
        public string IDNumber { get; set; }
        public string IDType { get; set; }
        public string Nationality { get; set; }
    }

    public class DeleteCustomerResponse
    {
        public string? Message { get; set; }
        public string? ErrorMessage { get; set; }
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);
    }

    public class UpdateCustomerRequest
    {
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? IDNumber { get; set; }
        public string? IDType { get; set; }
        public string? Nationality { get; set; }
    }

    public class UpdateCustomerResponse
    {
        public int CustomerId { get; set; }
        public int UserId { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? IDNumber { get; set; }
        public string? IDType { get; set; }
        public string? Nationality { get; set; }
        public DateTime CreatedDate { get; set; }
        public string Username { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
        public bool IsSuccess => string.IsNullOrEmpty(ErrorMessage);
    }

    public class GetAllCustomersResponse
    {
        public int CustomerId { get; set; }
        public int UserId { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? PostalCode { get; set; }
        public string? IDNumber { get; set; }
        public string? IDType { get; set; }
        public string? Nationality { get; set; }
        public DateTime CreatedDate { get; set; }
        public int TotalRecords { get; set; }
    }

    public class CustomerFilterRequest
    {
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 10;
        public string? SearchTerm { get; set; }
        public string? Country { get; set; }
        public string? City { get; set; }
        public string? SortBy { get; set; }
        public bool IsAscending { get; set; } = true;
    }
}
