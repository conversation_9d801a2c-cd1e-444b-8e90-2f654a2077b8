import axiosInstance from './axiosInstance';
import publicAxiosInstance from './publicAxiosInstance';
import axios from 'axios';

const roomService = {
  async getAllRooms() {
    try {
      // Sử dụng API proxy của Next.js
      const response = await axios.get('/api/rooms');
      return response.data;
    } catch (error: any) {
      throw new Error('Không thể tải danh sách phòng');
    }
  },

  async getAvailableRooms() {
    try {
      // Sử dụng API proxy của Next.js
      const response = await axios.get('/api/rooms/available');
      return response.data;
    } catch (error: any) {
      throw new Error('Không thể tải danh sách phòng trống');
    }
  },

  async getRoomById(id: number) {
    try {
      // Sử dụng API proxy của Next.js
      const response = await axios.get(`/api/rooms/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error('<PERSON>hông thể tải thông tin phòng');
    }
  }
};

export { roomService };
