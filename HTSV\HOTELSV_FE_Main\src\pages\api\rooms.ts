import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Thử gọi API backend
    try {
      const response = await axios.get('http://localhost:5001/api/Rooms/GetAllRoom', {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      // Nếu thành công, trả về dữ liệu từ backend
      return res.status(200).json(response.data);
    } catch (error) {
      // Nếu có lỗi, trả về thông báo lỗi
      return res.status(500).json({
        success: false,
        message: '<PERSON>hông thể kết nối đến API backend'
      });
    }
  } catch (error) {
    return res.status(500).json({
      success: false,
      message: 'Không thể tải danh sách phòng'
    });
  }
}
