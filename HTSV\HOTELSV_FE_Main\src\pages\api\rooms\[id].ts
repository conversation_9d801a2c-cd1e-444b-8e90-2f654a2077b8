import type { NextApiRequest, NextApiResponse } from 'next';
import axios from 'axios';

// Dữ liệu mẫu cho phòng
const sampleRooms = [
  {
    roomId: 1,
    roomNumber: '101',
    roomTypeId: 1,
    floor: 1,
    status: 'Available',
    cleaningStatus: 'Clean',
    roomTypeName: 'Standard',
    price: 500000,
    capacity: 2,
    description: 'Phòng tiêu chuẩn với đầy đủ tiện nghi cơ bản'
  },
  {
    roomId: 2,
    roomNumber: '102',
    roomTypeId: 1,
    floor: 1,
    status: 'Available',
    cleaningStatus: 'Clean',
    roomTypeName: 'Standard',
    price: 500000,
    capacity: 2,
    description: 'Phòng tiêu chuẩn với đầy đủ tiện nghi cơ bản'
  },
  {
    roomId: 3,
    roomNumber: '201',
    roomTypeId: 2,
    floor: 2,
    status: 'Available',
    cleaningStatus: 'Clean',
    roomTypeName: 'Deluxe',
    price: 800000,
    capacity: 2,
    description: '<PERSON><PERSON>ng cao cấp với view đẹp và tiện nghi hiện đại'
  },
  {
    roomId: 4,
    roomNumber: '202',
    roomTypeId: 2,
    floor: 2,
    status: 'Available',
    cleaningStatus: 'Clean',
    roomTypeName: 'Deluxe',
    price: 800000,
    capacity: 2,
    description: 'Phòng cao cấp với view đẹp và tiện nghi hiện đại'
  },
  {
    roomId: 5,
    roomNumber: '301',
    roomTypeId: 3,
    floor: 3,
    status: 'Available',
    cleaningStatus: 'Clean',
    roomTypeName: 'Suite',
    price: 1200000,
    capacity: 4,
    description: 'Phòng Suite rộng rãi với phòng khách riêng biệt'
  }
];

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const { id } = req.query;
  const roomId = parseInt(id as string);

  try {
    // Thử gọi API backend
    try {
      const response = await axios.get(`http://localhost:5001/api/Rooms/GetRoomBy/${roomId}`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      // Nếu thành công, trả về dữ liệu từ backend
      return res.status(200).json(response.data);
    } catch (error) {
      // Nếu có lỗi, trả về dữ liệu mẫu
      const room = sampleRooms.find(r => r.roomId === roomId);
      
      if (!room) {
        return res.status(404).json({
          success: false,
          message: `Không tìm thấy phòng với ID: ${roomId}`
        });
      }
      
      return res.status(200).json({
        success: true,
        data: room
      });
    }
  } catch (error) {
    return res.status(500).json({ 
      success: false,
      message: 'Không thể tải thông tin phòng' 
    });
  }
}
