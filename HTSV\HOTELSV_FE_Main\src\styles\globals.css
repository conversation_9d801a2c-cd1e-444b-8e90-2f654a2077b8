:root {
  --background: #0a0a0a;
  --foreground: #E0E0E0;
  --primary: #C0A080;
  --secondary: #1a1a1a;
  --accent: #D4AF37;
  --text: #E0E0E0;
  --border-color: #1a1a1a;
  --card-bg: #121212;
  --input-bg: rgba(18, 18, 18, 0.95);
  --dark-border: #1a1a1a;
  --darker-bg: #0f0f0f;
  --button-bg: #1a1a1a;
  --button-hover: #2a2a2a;
}

html,
body {
  padding: 0;
  margin: 0;
  background: #121212;
  color: #fff;
  min-height: 100vh;
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  font-family: system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
  letter-spacing: 0.3px;
}

h1, h2, h3, h4, h5, h6 {
  font-family: system-ui, -apple-system, 'Segoe UI', Robot<PERSON>, sans-serif;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text);
}

p, span, a, button, input, select, option, label {
  font-family: system-ui, -apple-system, 'Segoe UI', Roboto, sans-serif;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border-color: var(--border-color);
}

#__next {
  min-height: 100vh;
  background: #121212;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  background: var(--button-bg);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

button:hover {
  background: var(--button-hover);
}

/* Global style for select options */
select option {
  background-color: #121212;
  color: white;
}

.footer {
  background: rgba(18, 18, 18, 1);
  color: #fff;
  padding: 40px 0;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
}
