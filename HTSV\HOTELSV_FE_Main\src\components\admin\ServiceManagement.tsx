import React, { useState, useEffect } from 'react';
import { serviceApi, bookingServiceApi } from '../../services/adminService';
import styles from '../../styles/admin.module.css';
import { FaEdit, FaTrash, FaPlus, FaSearch, FaList } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface Service {
  serviceId: number;
  serviceName: string;
  description: string;
  price: number;
  isActive: boolean;
}

interface BookingService {
  bookingServiceId: number;
  bookingId: number;
  serviceId: number;
  serviceName: string;
  quantity: number;
  price: number;
  serviceDate: string;
  status: string;
  notes: string;
  requestedBy?: number;
  servedBy?: number;
}

const ServiceManagement: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [bookingServices, setBookingServices] = useState<BookingService[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showBookingServicesModal, setShowBookingServicesModal] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [currentService, setCurrentService] = useState<Service | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'services' | 'bookingServices'>('services');
  
  const [formData, setFormData] = useState({
    serviceName: '',
    description: '',
    price: 0,
    isActive: true
  });

  useEffect(() => {
    if (activeTab === 'services') {
      fetchServices();
    } else {
      fetchBookingServices();
    }
  }, [activeTab]);

  const fetchServices = async () => {
    try {
      setLoading(true);
      const response = await serviceApi.getAllServices();
      setServices(response);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const fetchBookingServices = async () => {
    try {
      setLoading(true);
      const response = await bookingServiceApi.getAllBookingServices();
      if (response.success) {
        setBookingServices(response.data);
      }
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' 
        ? (e.target as HTMLInputElement).checked 
        : type === 'number' || name === 'price' 
          ? parseFloat(value) 
          : value
    });
  };

  const handleAddService = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await serviceApi.addService(formData);
      toast.success('Thêm dịch vụ thành công');
      fetchServices();
      setShowModal(false);
      resetForm();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleUpdateService = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentService) return;
    
    try {
      const response = await serviceApi.updateService(currentService.serviceId, formData);
      toast.success('Cập nhật dịch vụ thành công');
      fetchServices();
      setShowModal(false);
      resetForm();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleDeleteService = async (id: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')) {
      try {
        const response = await serviceApi.deleteService(id);
        toast.success('Xóa dịch vụ thành công');
        fetchServices();
      } catch (error: any) {
        toast.error(error.message);
      }
    }
  };

  const handleDeleteBookingService = async (id: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa dịch vụ đặt phòng này?')) {
      try {
        const response = await bookingServiceApi.deleteBookingService(id);
        toast.success('Xóa dịch vụ đặt phòng thành công');
        fetchBookingServices();
      } catch (error: any) {
        toast.error(error.message);
      }
    }
  };

  const openEditModal = (service: Service) => {
    setCurrentService(service);
    setFormData({
      serviceName: service.serviceName,
      description: service.description,
      price: service.price,
      isActive: service.isActive
    });
    setIsEditing(true);
    setShowModal(true);
  };

  const openAddModal = () => {
    resetForm();
    setIsEditing(false);
    setShowModal(true);
  };

  const resetForm = () => {
    setFormData({
      serviceName: '',
      description: '',
      price: 0,
      isActive: true
    });
    setCurrentService(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  const filteredServices = services.filter(service => 
    service.serviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredBookingServices = bookingServices.filter(service => 
    service.serviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.bookingId.toString().includes(searchTerm)
  );

  return (
    <div>
      <div className={styles.toolbarSection}>
        <div className={styles.searchBox}>
          <FaSearch />
          <input
            type="text"
            placeholder="Tìm kiếm dịch vụ..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className={styles.filterContainer}>
          <button 
            className={`${styles.button} ${activeTab === 'services' ? styles.primaryButton : styles.secondaryButton}`}
            onClick={() => setActiveTab('services')}
          >
            Dịch vụ
          </button>
          <button 
            className={`${styles.button} ${activeTab === 'bookingServices' ? styles.primaryButton : styles.secondaryButton}`}
            onClick={() => setActiveTab('bookingServices')}
          >
            Dịch vụ đặt phòng
          </button>
          {activeTab === 'services' && (
            <button className={`${styles.button} ${styles.successButton}`} onClick={openAddModal}>
              <FaPlus /> Thêm dịch vụ
            </button>
          )}
        </div>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      ) : activeTab === 'services' ? (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Tên dịch vụ</th>
                <th>Mô tả</th>
                <th>Giá</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredServices.length > 0 ? (
                filteredServices.map((service) => (
                  <tr key={service.serviceId}>
                    <td>{service.serviceId}</td>
                    <td>{service.serviceName}</td>
                    <td>{service.description.length > 50 
                      ? `${service.description.substring(0, 50)}...` 
                      : service.description}
                    </td>
                    <td>${service.price.toFixed(2)}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${service.isActive ? styles.statusAvailable : styles.statusBooked}`}>
                        {service.isActive ? 'Hoạt động' : 'Không hoạt động'}
                      </span>
                    </td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.editButton}`}
                        onClick={() => openEditModal(service)}
                      >
                        <FaEdit />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteService(service.serviceId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={6} style={{ textAlign: 'center' }}>
                    Không tìm thấy dịch vụ nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Đặt phòng ID</th>
                <th>Dịch vụ</th>
                <th>Số lượng</th>
                <th>Giá</th>
                <th>Ngày sử dụng</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookingServices.length > 0 ? (
                filteredBookingServices.map((service) => (
                  <tr key={service.bookingServiceId}>
                    <td>{service.bookingServiceId}</td>
                    <td>{service.bookingId}</td>
                    <td>{service.serviceName}</td>
                    <td>{service.quantity}</td>
                    <td>${service.price.toFixed(2)}</td>
                    <td>{formatDate(service.serviceDate)}</td>
                    <td>
                      <span className={`${styles.statusBadge} ${styles[`status${service.status}`]}`}>
                        {service.status}
                      </span>
                    </td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteBookingService(service.bookingServiceId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={8} style={{ textAlign: 'center' }}>
                    Không tìm thấy dịch vụ đặt phòng nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal for adding/editing service */}
      {showModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2 className={styles.modalTitle}>
                {isEditing ? 'Chỉnh sửa dịch vụ' : 'Thêm dịch vụ mới'}
              </h2>
              <button className={styles.closeButton} onClick={() => setShowModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={isEditing ? handleUpdateService : handleAddService}>
              <div className={styles.formGroup}>
                <label>Tên dịch vụ</label>
                <input
                  type="text"
                  name="serviceName"
                  value={formData.serviceName}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Mô tả</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Giá</label>
                <input
                  type="number"
                  name="price"
                  min="0"
                  step="0.01"
                  value={formData.price}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                  />
                  Đang hoạt động
                </label>
              </div>
              <div className={styles.buttonContainer}>
                <button
                  type="button"
                  className={`${styles.button} ${styles.secondaryButton}`}
                  onClick={() => setShowModal(false)}
                >
                  Hủy
                </button>
                <button type="submit" className={`${styles.button} ${styles.primaryButton}`}>
                  {isEditing ? 'Cập nhật' : 'Thêm mới'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default ServiceManagement;
