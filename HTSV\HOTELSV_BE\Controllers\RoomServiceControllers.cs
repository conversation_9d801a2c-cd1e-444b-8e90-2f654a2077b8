using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using System.Data;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class RoomServicesController : ControllerBase
    {
        private readonly string _connectionString;

        public RoomServicesController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpGet("GetAllRoomServices")]
        [RequirePermission("view_roomservices")]
        public async Task<IActionResult> GetAllRoomServices()
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var roomServices = await connection.QueryAsync<RoomService>("SELECT * FROM RoomServices");
                return Ok(new {
                    success = true,
                    data = roomServices
                });
            }
        }

        [HttpGet("GetRoomServicesBy/{id}")]
        [RequirePermission("view_roomservices")]
        public async Task<IActionResult> GetRoomServicesById(int id)
        {
            using (var connection = new SqlConnection(_connectionString))
            {
                var roomServices = await connection.QueryFirstOrDefaultAsync<RoomService>(
                    "SELECT * FROM RoomServices WHERE ServiceId = @Id",
                    new { Id = id });

                if (roomServices == null)
                    return NotFound(new { 
                        success = false,
                        message = "Không tìm thấy dịch vụ phòng với ID này." 
                    });

                return Ok(new {
                    success = true,
                    data = roomServices
                });
            }
        }

        [HttpPost("AddRoomService")]
        [RequirePermission("create_roomservice")]
        public async Task<ActionResult> AddRoomService(AddRoomService addRoomservice)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@ServiceName", addRoomservice.ServiceName);
                    parameters.Add("@Description", addRoomservice.Description);
                    parameters.Add("@Price", addRoomservice.Price);
                    parameters.Add("@IsActive", addRoomservice.IsActive);

                    var result = await connection.QueryFirstOrDefaultAsync<RoomService>(
                        "sp_AddRoomService",
                        parameters,
                        commandType: CommandType.StoredProcedure);

                    return Ok(new {
                        success = true,
                        message = "Thêm dịch vụ phòng thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { 
                    success = false,
                    message = "Có lỗi xảy ra khi thêm dịch vụ phòng.", 
                    error = ex.Message 
                });
            }
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("edit_roomservice")]
        public async Task<IActionResult> Update(int id, AddRoomService updateroomService)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@ServiceId", id);
                    parameters.Add("@ServiceName", updateroomService.ServiceName);
                    parameters.Add("@Description", updateroomService.Description);
                    parameters.Add("@Price", updateroomService.Price);
                    parameters.Add("@IsActive", updateroomService.IsActive);

                    var result = await connection.QueryFirstOrDefaultAsync<RoomService>(
                        "sp_UpdateRoomService",
                        parameters,
                        commandType: CommandType.StoredProcedure);

                    if (result == null)
                        return NotFound(new { 
                            success = false,
                            message = "Không tìm thấy dịch vụ phòng với ID này" 
                        });

                    return Ok(new {
                        success = true,
                        message = "Cập nhật dịch vụ phòng thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi cập nhật dịch vụ phòng",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("DeleteRoomServiceBy/{id}")]
        [RequirePermission("cancel_roomservice")]
        public async Task<IActionResult> DeleteRoomService(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var result = await connection.ExecuteAsync(
                        "DELETE FROM RoomServices WHERE ServiceId = @Id",
                        new { Id = id });

                    if (result == 0)
                        return NotFound(new { 
                            success = false,
                            message = "Không tìm thấy dịch vụ phòng với ID này" 
                        });

                    return Ok(new { 
                        success = true,
                        message = "Xóa dịch vụ phòng thành công" 
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new {
                    success = false,
                    message = "Lỗi khi xóa dịch vụ phòng",
                    error = ex.Message
                });
            }
        }

        [HttpGet]
        [RequirePermission("view_roomservices")]
        public async Task<ActionResult<PaginatedResponse<RoomService>>> GetRoomServices([FromQuery] RoomServiceFilterRequest filter)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@PageNumber", filter.PageNumber);
                    parameters.Add("@PageSize", filter.PageSize);
                    parameters.Add("@SearchTerm", filter.SearchTerm);
                    parameters.Add("@MinPrice", filter.MinPrice);
                    parameters.Add("@MaxPrice", filter.MaxPrice);
                    parameters.Add("@SortBy", filter.SortBy ?? "Name");
                    parameters.Add("@IsAscending", filter.IsAscending);

                    var result = await connection.QueryAsync<RoomService>(
                        "sp_GetRoomServicesPaginated",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var services = result.ToList();
                    var totalItems = services.Any() ? services.First().TotalRecords : 0;

                    var paginatedResponse = new PaginatedResponse<RoomService>(
                        services,
                        totalItems,
                        filter.PageNumber,
                        filter.PageSize
                    );

                    return Ok(new
                    {
                        success = true,
                        data = paginatedResponse
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách dịch vụ phòng",
                    error = ex.Message
                });
            }
        }
    }
}