using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using HOTELSV_BE.Models;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class BookingController : ControllerBase
    {
        private readonly string _connectionString;
        private readonly ILogger<BookingController> _logger;

        public BookingController(IConfiguration configuration, ILogger<BookingController> logger)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
            _logger = logger;
        }

        [HttpPost("Add")]
        [RequirePermission("create_booking")]
        public async Task<IActionResult> AddBooking([FromBody] BookingRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@CustomerId", request.CustomerId);
                    parameters.Add("@EmployeeId", request.EmployeeId);
                    parameters.Add("@CheckInDate", request.CheckInDate);
                    parameters.Add("@CheckOutDate", request.CheckOutDate);
                    parameters.Add("@RoomTypeId", request.RoomTypeId);
                    parameters.Add("@Quantity", request.Quantity);
                    parameters.Add("@Notes", request.Notes);
                    parameters.Add("@BookingSource", request.BookingSource);

                    var result = await connection.QueryAsync<dynamic>(
                        "sp_AddBooking",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var firstResult = result.FirstOrDefault();

                    // Check if there's an error message
                    if (firstResult?.ErrorMessage != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = firstResult.ErrorMessage
                        });
                    }

                    // Map the results to BookingDetailResponse
                    var bookingDetails = result.Select(r => new BookingDetailResponse
                    {
                        BookingId = r.BookingId,
                        CustomerId = r.CustomerId,
                        EmployeeId = r.EmployeeId,
                        BookingDate = r.BookingDate,
                        CheckInDate = r.CheckInDate,
                        CheckOutDate = r.CheckOutDate,
                        TotalAmount = r.TotalAmount,
                        BookingStatus = r.BookingStatus,
                        PaymentStatus = r.PaymentStatus,
                        Notes = r.Notes,
                        BookingSource = r.BookingSource,
                        RoomTypeId = r.RoomTypeId,
                        Name = r.Name,
                        RoomTypeQuantity = r.RoomTypeQuantity,
                        RoomTypePrice = r.RoomTypePrice,
                        RoomId = r.RoomId,
                        RoomNumber = r.RoomNumber,
                        RoomCheckInDate = r.RoomCheckInDate,
                        RoomCheckOutDate = r.RoomCheckOutDate,
                        RoomStatus = r.RoomStatus,
                        AssignedBy = r.AssignedBy
                    }).ToList();

                    return Ok(new
                    {
                        success = true,
                        message = "Đặt phòng thành công",
                        data = bookingDetails
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in AddBooking");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }
        [HttpPut("Update/{id}")]
        [RequirePermission("edit_booking")]
        public async Task<IActionResult> UpdateBooking(int id, [FromBody] UpdateBookingRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingId", id);
                    parameters.Add("@CustomerId", request.CustomerId);
                    parameters.Add("@EmployeeId", request.EmployeeId);
                    parameters.Add("@CheckInDate", request.CheckInDate);
                    parameters.Add("@CheckOutDate", request.CheckOutDate);
                    parameters.Add("@TotalAmount", request.TotalAmount);
                    parameters.Add("@Status", request.Status);
                    parameters.Add("@PaymentStatus", request.PaymentStatus);
                    parameters.Add("@Notes", request.Notes);
                    parameters.Add("@BookingSource", request.BookingSource);

                    var result = await connection.QueryAsync<dynamic>(
                        "sp_UpdateBooking",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var firstResult = result.FirstOrDefault();

                    // Kiểm tra nếu có lỗi
                    if (firstResult?.ErrorMessage != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = firstResult.ErrorMessage
                        });
                    }

                    // Map kết quả vào response model
                    var bookingDetails = new UpdateBookingResponse
                    {
                        BookingId = firstResult.BookingId,
                        CustomerId = firstResult.CustomerId,
                        EmployeeId = firstResult.EmployeeId,
                        BookingDate = firstResult.BookingDate,
                        CheckInDate = firstResult.CheckInDate,
                        CheckOutDate = firstResult.CheckOutDate,
                        TotalAmount = firstResult.TotalAmount,
                        Status = firstResult.Status,
                        PaymentStatus = firstResult.PaymentStatus,
                        Notes = firstResult.Notes,
                        BookingSource = firstResult.BookingSource,
                        CustomerName = firstResult.CustomerName,
                        EmployeeName = firstResult.EmployeeName
                    };

                    return Ok(new
                    {
                        success = true,
                        message = "Cập nhật đặt phòng thành công",
                        data = bookingDetails
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi cập nhật đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }
        [HttpPut("UpdateStatus/{id}")]
        [RequirePermission("edit_booking")]
        public async Task<IActionResult> UpdateBookingStatus(int id, [FromBody] UpdateBookingStatusRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingId", id);
                    parameters.Add("@Status", request.Status);
                    parameters.Add("@PaymentStatus", request.PaymentStatus);

                    var result = await connection.QueryAsync<dynamic>(
                        "sp_UpdateBookingStatus",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var firstResult = result.FirstOrDefault();

                    // Kiểm tra nếu có lỗi
                    if (firstResult?.ErrorMessage != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = firstResult.ErrorMessage
                        });
                    }

                    // Map kết quả vào response model
                    var bookingStatus = new BookingStatusResponse
                    {
                        BookingId = firstResult.BookingId,
                        CustomerId = firstResult.CustomerId,
                        EmployeeId = firstResult.EmployeeId,
                        BookingDate = firstResult.BookingDate,
                        CheckInDate = firstResult.CheckInDate,
                        CheckOutDate = firstResult.CheckOutDate,
                        TotalAmount = firstResult.TotalAmount,
                        Status = firstResult.Status,
                        PaymentStatus = firstResult.PaymentStatus,
                        Notes = firstResult.Notes,
                        BookingSource = firstResult.BookingSource,
                        CustomerName = firstResult.CustomerName,
                        EmployeeName = firstResult.EmployeeName
                    };

                    return Ok(new
                    {
                        success = true,
                        message = "Cập nhật trạng thái đặt phòng thành công",
                        data = bookingStatus
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi cập nhật trạng thái đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }

        [HttpDelete("{id}")]
        [RequirePermission("cancel_booking")]
        public async Task<IActionResult> DeleteBooking(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingId", id);

                    var result = await connection.QueryFirstOrDefaultAsync<dynamic>(
                        "sp_DeleteBooking",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result.ErrorMessage != null)
                    {
                        return BadRequest(new
                        {
                            success = false,
                            message = result.ErrorMessage
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Xóa đặt phòng thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi xóa đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server, vui lòng thử lại sau"
                });
            }
        }

        [HttpGet("Details/{id}")]
        [RequirePermission("view_bookings")]
        public async Task<IActionResult> GetBookingDetails(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@BookingId", id);

                    var bookingDetails = await connection.QueryFirstOrDefaultAsync<BookingDetailsResponse>(
                        "sp_GetBookingDetails",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (bookingDetails == null)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = "Không tìm thấy thông tin đặt phòng"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Lấy thông tin đặt phòng thành công",
                        data = bookingDetails
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lấy chi tiết đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server khi lấy thông tin đặt phòng"
                });
            }
        }

        [HttpGet("GetAll")]
        [RequirePermission("view_bookings")]
        public async Task<IActionResult> GetAllBookings()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    // Sử dụng query trực tiếp để đảm bảo lấy đủ dữ liệu
                    var sql = @"
                        SELECT
                            b.BookingId,
                            b.CustomerId,
                            b.EmployeeId,
                            b.BookingDate,
                            b.CheckInDate,
                            b.CheckOutDate,
                            b.TotalAmount,
                            b.Status as BookingStatus,
                            b.PaymentStatus,
                            b.Notes,
                            b.BookingSource,
                            rt.RoomTypeId,
                            rt.Name,
                            br.Quantity as RoomTypeQuantity,
                            rt.Price as RoomTypePrice,
                            r.RoomId,
                            r.RoomNumber,
                            ra.CheckInDate as RoomCheckInDate,
                            ra.CheckOutDate as RoomCheckOutDate,
                            r.Status as RoomStatus,
                            ra.AssignedBy
                        FROM Bookings b
                        LEFT JOIN BookingRooms br ON b.BookingId = br.BookingId
                        LEFT JOIN RoomTypes rt ON br.RoomTypeId = rt.RoomTypeId
                        LEFT JOIN RoomAssignments ra ON b.BookingId = ra.BookingId
                        LEFT JOIN Rooms r ON ra.RoomId = r.RoomId
                        ORDER BY b.BookingDate DESC";

                    var bookings = await connection.QueryAsync<BookingDetailResponse>(sql);

                    if (!bookings.Any())
                    {
                        return Ok(new
                        {
                            success = true,
                            message = "Không có dữ liệu đặt phòng",
                            data = new List<BookingDetailResponse>()
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Lấy danh sách đặt phòng thành công",
                        data = bookings
                    });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Lỗi khi lấy danh sách đặt phòng");
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi server khi lấy danh sách đặt phòng"
                });
            }
        }

        [HttpGet]
        [RequirePermission("view_bookings")]
        public async Task<ActionResult<PaginatedResponse<BookingModels>>> GetBookings([FromQuery] BookingFilterRequest filter)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@PageNumber", filter.PageNumber);
                    parameters.Add("@PageSize", filter.PageSize);
                    parameters.Add("@SearchTerm", filter.SearchTerm);
                    parameters.Add("@Status", filter.Status);
                    parameters.Add("@FromDate", filter.FromDate);
                    parameters.Add("@ToDate", filter.ToDate);
                    parameters.Add("@SortBy", filter.SortBy ?? "BookingDate");
                    parameters.Add("@IsAscending", filter.IsAscending);

                    var result = await connection.QueryAsync<BookingModels>(
                        "sp_GetBookingsPaginated",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var bookings = result.ToList();
                    var totalItems = bookings.Any() ? bookings.First().TotalRecords : 0;

                    var paginatedResponse = new PaginatedResponse<BookingModels>(
                        bookings,
                        totalItems,
                        filter.PageNumber,
                        filter.PageSize
                    );

                    return Ok(new
                    {
                        success = true,
                        data = paginatedResponse
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách booking",
                    error = ex.Message
                });
            }
        }
    }
}