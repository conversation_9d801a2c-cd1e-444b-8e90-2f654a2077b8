import axios from 'axios';


// Base URL từ biến môi trường hoặc mặc định
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5001/api';

// Axios instance cho các request không cần xác thực
export const publicApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Axios instance cho các request cần xác thực
export const privateApi = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000,
});

// Thêm interceptor để xử lý token
privateApi.interceptors.request.use(
  (config) => {
    // Thực hiện trên client-side
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    console.error('Request Error:', error);
    return Promise.reject(error);
  }
);

// Xử lý response và lỗi chung cho cả hai instance
const handleResponse = (promise: Promise<any>, isPrivateApi: boolean = false) => {
  return promise
    .then(response => {
      console.log('API Response:', response);

      // Kiểm tra cấu trúc response
      if (response.data && response.data.success === false) {
        return Promise.reject(new Error(response.data.message || 'Có lỗi xảy ra'));
      }
      return response.data;
    })
    .catch(error => {
      console.error('API Error:', error);

      // Xử lý lỗi mạng
      if (!error.response) {
        throw new Error('Lỗi kết nối mạng - vui lòng kiểm tra kết nối internet');
      }

      // Xử lý lỗi 401 - Unauthorized
      if (error.response.status === 401) {
        // Nếu là API riêng tư (cần xác thực) và ở client-side, xóa token và thông tin người dùng
        if (isPrivateApi && typeof window !== 'undefined') {
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          // Redirect đến trang đăng nhập nếu cần
          if (window.location.pathname !== '/auth/login') {
            window.location.href = '/auth/login';
          }
          throw new Error('Phiên đăng nhập hết hạn, vui lòng đăng nhập lại');
        }
      }

      // Xử lý lỗi 405 - Method Not Allowed
      if (error.response.status === 405) {
        console.error('Method Not Allowed Error:', error.response);
        throw new Error('Phương thức không được phép. Vui lòng liên hệ quản trị viên.');
      }

      // Xử lý các lỗi khác
      const errorMessage = error.response?.data?.message || error.message || 'Có lỗi xảy ra';
      throw error; // Trả về toàn bộ đối tượng lỗi để xử lý chi tiết hơn ở component
    });
};

// API Services
export const authService = {
  login: (credentials: { email?: string; userName?: string; password: string }) => {
    return handleResponse(publicApi.post('/Auth/login', credentials));
  },

  register: (userData: any) => {
    return handleResponse(publicApi.post('/Auth/register', userData));
  },
};

export const roomService = {
  getAllRooms: () => {
    return handleResponse(publicApi.get('/Rooms/GetAllRoom'));
  },

  getAvailableRooms: () => {
    return handleResponse(publicApi.get('/Rooms/GetAvailableRoom'));
  },

  getRoomById: (id: number) => {
    return handleResponse(publicApi.get(`/Rooms/GetRoomBy/${id}`));
  },

  addRoom: (roomData: any) => {
    return handleResponse(privateApi.post('/Rooms/AddRoom', roomData), true);
  },

  updateRoom: (id: number, roomData: any) => {
    return handleResponse(privateApi.put(`/Rooms/UpdateRoom/${id}`, roomData), true);
  },

  deleteRoom: (id: number) => {
    return handleResponse(privateApi.delete(`/Rooms/Delete/${id}`), true);
  },
};

export const bookingService = {
  addBooking: (bookingData: any) => {
    return handleResponse(privateApi.post('/Booking/Add', bookingData), true);
  },

  getBookingsByUser: (userId: number) => {
    // Nếu có endpoint riêng cho user thì sửa lại, ví dụ: `/Booking/User/${userId}`
    return handleResponse(privateApi.get(`/Booking?userId=${userId}`), true);
  },

  getBookingById: (id: number) => {
    return handleResponse(privateApi.get(`/Booking/Details/${id}`), true);
  },

  updateBookingStatus: (id: number, statusData: { status: string, paymentStatus?: string }) => {
    return handleResponse(privateApi.put(`/Booking/UpdateStatus/${id}`, statusData), true);
  },

  getAllBookings: () => {
    return handleResponse(privateApi.get('/Booking/GetAll'), true);
  },

  updateBooking: (id: number, bookingData: any) => {
    return handleResponse(privateApi.put(`/Booking/Update/${id}`, bookingData), true);
  },

  deleteBooking: (id: number) => {
    return handleResponse(privateApi.delete(`/Booking/${id}`), true);
  }
};

export const roomTypeService = {
  getAllRoomTypes: () => {
    return handleResponse(publicApi.get('/RoomType/GetAll'));
  },

  getRoomTypeById: (id: number) => {
    return handleResponse(publicApi.get(`/RoomType/${id}`));
  },
};

export const roomServiceApi = {
  getAllServices: () => {
    return handleResponse(publicApi.get('/RoomService/GetAll'));
  },

  getServiceById: (id: number) => {
    return handleResponse(publicApi.get(`/RoomService/${id}`));
  },
};

export const userService = {
  getCurrentUser: () => {
    return handleResponse(privateApi.get('/User/Current'), true);
  },

  updateProfile: (userData: any) => {
    return handleResponse(privateApi.put('/User/Update', userData), true);
  },

  // Thêm các hàm cho quản lý user
  getAllUsers: () => {
    return handleResponse(privateApi.get('/User/GetAll'), true);
  },

  getUserById: (userId: number) => {
    return handleResponse(privateApi.get(`/User/GetByID/${userId}`), true);
  },

  deleteUser: (userId: number) => {
    return handleResponse(privateApi.delete(`/User/Delete/${userId}`), true);
  },

  createUser: (userData: any) => {
    return handleResponse(privateApi.post('/User/Create', userData), true);
  },

  updateUser: (userId: number, userData: any) => {
    return handleResponse(privateApi.put(`/User/Update/${userId}`, userData), true);
  },
};

// Không sử dụng dữ liệu giả nữa, tất cả đều sử dụng API thật
