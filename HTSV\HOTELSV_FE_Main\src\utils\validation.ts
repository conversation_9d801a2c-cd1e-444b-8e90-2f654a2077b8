export const validateEmail = (email: string): string => {
  if (!email) return 'Email là bắt buộc';
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) return 'Email không hợp lệ';
  return '';
};

export const validatePassword = (password: string): string => {
  if (!password) return 'Mật khẩu là bắt buộc';
  if (password.length < 6) return 'Mật khẩu phải có ít nhất 6 ký tự';
  return '';
};

export const validateUsername = (username: string): string => {
  if (!username) return 'Tên người dùng là bắt buộc';
  if (username.length < 3) return 'Tên người dùng phải có ít nhất 3 ký tự';
  return '';
};

export const validateConfirmPassword = (password: string, confirmPassword: string): string => {
  if (!confirmPassword) return '<PERSON><PERSON><PERSON> nhận mật khẩu là bắt buộc';
  if (password !== confirmPassword) return 'Mật khẩu không khớp';
  return '';
};
