using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Data.SqlClient;
using Dapper;
using System.Data;
using HOTELSV_BE.Models;
using HOTELSV_BE.Attributes;

namespace HOTELSV_BE.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class UserController : ControllerBase
    {
        private readonly string _connectionString;

        public UserController(IConfiguration configuration)
        {
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        [HttpPut("Update/{id}")]
        [RequirePermission("edit_user")]
        public async Task<ActionResult> UpdateUser(int id, UpdateUserModels user)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@UserId", id);
                    parameters.Add("@Username", user.Username);
                    parameters.Add("@PasswordHash", user.Password); // Note: Should hash password before saving
                    parameters.Add("@Email", user.Email);
                    parameters.Add("@FirstName", user.FirstName);
                    parameters.Add("@LastName", user.LastName);
                    parameters.Add("@Phone", user.Phone);
                    parameters.Add("@IsActive", user.IsActive);

                    var result = await connection.QueryFirstOrDefaultAsync<UserModels>(
                        "sp_UpdateUser",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == null)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = $"Không tìm thấy người dùng với ID: {id}"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Cập nhật thông tin người dùng thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi cập nhật thông tin người dùng",
                    error = ex.Message
                });
            }
        }

        [HttpGet("GetByID/{id}")]
        [RequirePermission("view_users")]
        public async Task<ActionResult<UserModels>> GetUserById(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var user = await connection.QueryFirstOrDefaultAsync<UserModels>(
                        "GetUserById",
                        new { UserId = id },
                        commandType: CommandType.StoredProcedure
                    );

                    if (user == null)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = $"Không tìm thấy người dùng với ID: {id}"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        data = user
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy thông tin người dùng",
                    error = ex.Message
                });
            }
        }

        [HttpGet("GetAll")]
        [RequirePermission("view_users")]
        public async Task<ActionResult<IEnumerable<UserModels>>> GetAllUsers()
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var users = await connection.QueryAsync<UserModels>(
                        "GetAllUsers",
                        commandType: CommandType.StoredProcedure
                    );

                    if (!users.Any())
                    {
                        return Ok(new
                        {
                            success = true,
                            message = "Không có dữ liệu người dùng",
                            data = new List<UserModels>()
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Lấy danh sách người dùng thành công",
                        data = users
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách người dùng",
                    error = ex.Message
                });
            }
        }

        [HttpPost("Add")]
        [RequirePermission("create_user")]
        public async Task<ActionResult> AddUser([FromBody] AddUserRequest request)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@Username", request.Username);
                    parameters.Add("@PasswordHash", request.PasswordHash);
                    parameters.Add("@Email", request.Email);
                    parameters.Add("@FirstName", request.FirstName);
                    parameters.Add("@LastName", request.LastName);
                    parameters.Add("@Phone", request.Phone);

                    var result = await connection.QueryFirstOrDefaultAsync<UserModels>(
                        "sp_AddUser",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    return Ok(new
                    {
                        success = true,
                        message = "Thêm người dùng thành công",
                        data = result
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi thêm người dùng",
                    error = ex.Message
                });
            }
        }

        [HttpDelete("Delete/{id}")]
        [RequirePermission("cancel_user")]
        public async Task<ActionResult> DeleteUser(int id)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var result = await connection.ExecuteAsync(
                        "sp_DeleteUser",
                        new { UserId = id },
                        commandType: CommandType.StoredProcedure
                    );

                    if (result == 0)
                    {
                        return NotFound(new
                        {
                            success = false,
                            message = $"Không tìm thấy người dùng với ID: {id}"
                        });
                    }

                    return Ok(new
                    {
                        success = true,
                        message = "Xóa người dùng thành công"
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi xóa người dùng",
                    error = ex.Message
                });
            }
        }

        [HttpGet]
        [RequirePermission("view_users")]
        public async Task<ActionResult<PaginatedResponse<UserModels>>> GetUsers([FromQuery] UserFilterRequest filter)
        {
            try
            {
                using (var connection = new SqlConnection(_connectionString))
                {
                    var parameters = new DynamicParameters();
                    parameters.Add("@PageNumber", filter.PageNumber);
                    parameters.Add("@PageSize", filter.PageSize);
                    parameters.Add("@SearchTerm", filter.SearchTerm);
                    parameters.Add("@IsActive", filter.IsActive);
                    parameters.Add("@SortBy", filter.SortBy ?? "Username");
                    parameters.Add("@IsAscending", filter.IsAscending);

                    var result = await connection.QueryAsync<UserModels>(
                        "sp_GetUsersPaginated",
                        parameters,
                        commandType: CommandType.StoredProcedure
                    );

                    var users = result.ToList();
                    var totalItems = users.Any() ? users.First().TotalRecords : 0;

                    var paginatedResponse = new PaginatedResponse<UserModels>(
                        users,
                        totalItems,
                        filter.PageNumber,
                        filter.PageSize
                    );

                    return Ok(new
                    {
                        success = true,
                        data = paginatedResponse
                    });
                }
            }
            catch (Exception ex)
            {
                return StatusCode(500, new
                {
                    success = false,
                    message = "Lỗi khi lấy danh sách người dùng",
                    error = ex.Message
                });
            }
        }
    }
}