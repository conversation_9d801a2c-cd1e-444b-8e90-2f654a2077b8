import React, { useState, useEffect } from 'react';
import { bookingApi } from '../../services/adminService';
import { bookingService } from '../../services/apiService';
import styles from '../../styles/admin.module.css';
import { FaE<PERSON>, Fa<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fa<PERSON>earch, FaPlus } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface Booking {
  bookingId: number;
  customerId: number;
  customerName: string;
  employeeId: number;
  employeeName: string;
  bookingDate: string;
  checkInDate: string;
  checkOutDate: string;
  totalAmount: number;
  status: string;
  paymentStatus: string;
  notes: string;
  bookingSource: string;
}

interface BookingDetail {
  bookingId: number;
  customerId: number;
  employeeId: number;
  bookingDate: string;
  checkInDate: string;
  checkOutDate: string;
  totalAmount: number;
  bookingStatus: string;
  paymentStatus: string;
  notes: string;
  bookingSource: string;
  roomTypeId: number;
  name: string;
  roomTypeQuantity: number;
  roomTypePrice: number;
  roomId?: number;
  roomNumber?: string;
  roomCheckInDate?: string;
  roomCheckOutDate?: string;
  roomStatus?: string;
  assignedBy?: number;
}

const BookingManagement: React.FC = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [showDetailModal, setShowDetailModal] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [currentBooking, setCurrentBooking] = useState<Booking | null>(null);
  const [currentBookingDetail, setCurrentBookingDetail] = useState<BookingDetail | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const [formData, setFormData] = useState({
    customerId: 0,
    employeeId: 0,
    checkInDate: '',
    checkOutDate: '',
    status: 'Pending',
    paymentStatus: 'Unpaid',
    notes: '',
    bookingSource: 'Website'
  });

  useEffect(() => {
    fetchBookings();
  }, []);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      // Sử dụng API thực để lấy danh sách đặt phòng
      const response = await bookingService.getAllBookings();
      console.log('Booking response:', response);

      if (response.success && Array.isArray(response.data)) {
        // Chuyển đổi dữ liệu từ API sang định dạng Booking
        const formattedBookings = response.data.map((booking: any) => ({
          bookingId: booking.bookingId,
          customerId: booking.customerId,
          customerName: booking.customerName || 'Khách hàng',
          employeeId: booking.employeeId || 0,
          employeeName: booking.employeeName || '',
          bookingDate: booking.bookingDate || new Date().toISOString(),
          checkInDate: booking.checkInDate,
          checkOutDate: booking.checkOutDate,
          totalAmount: booking.totalAmount,
          status: booking.status || 'Pending',
          paymentStatus: booking.paymentStatus || 'Unpaid',
          notes: booking.notes || '',
          bookingSource: booking.bookingSource || 'Website'
        }));

        setBookings(formattedBookings);
      } else {
        toast.error('Không thể tải danh sách đặt phòng: Dữ liệu không đúng định dạng');
      }
    } catch (error: any) {
      console.error('Error fetching bookings:', error);
      toast.error(error.message || 'Không thể tải danh sách đặt phòng');
    } finally {
      setLoading(false);
    }
  };

  const fetchBookingDetail = async (id: number) => {
    try {
      setLoading(true);
      // Sử dụng API thực để lấy chi tiết đặt phòng
      const response = await bookingService.getBookingById(id);
      console.log('Booking detail response:', response);

      if (response.success && response.data) {
        // Chuyển đổi dữ liệu từ API sang định dạng BookingDetail
        const bookingDetail: BookingDetail = {
          bookingId: response.data.bookingId,
          customerId: response.data.customerId,
          employeeId: response.data.employeeId || 0,
          bookingDate: response.data.bookingDate || response.data.createdAt || new Date().toISOString(),
          checkInDate: response.data.checkInDate,
          checkOutDate: response.data.checkOutDate,
          totalAmount: response.data.totalAmount,
          bookingStatus: response.data.status || 'Pending',
          paymentStatus: response.data.paymentStatus || 'Unpaid',
          notes: response.data.notes || '',
          bookingSource: response.data.bookingSource || 'Website',
          roomTypeId: response.data.roomTypeId || 0,
          name: response.data.roomType || response.data.name || 'Không xác định',
          roomTypeQuantity: response.data.quantity || 1,
          roomTypePrice: response.data.price || response.data.totalAmount,
          roomId: response.data.roomId,
          roomNumber: response.data.roomNumber,
          roomStatus: response.data.roomStatus
        };

        setCurrentBookingDetail(bookingDetail);
        setShowDetailModal(true);
      } else {
        toast.error('Không thể tải chi tiết đặt phòng: Dữ liệu không đúng định dạng');
      }
    } catch (error: any) {
      console.error('Error fetching booking detail:', error);
      toast.error(error.message || 'Không thể tải chi tiết đặt phòng');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: name === 'customerId' || name === 'employeeId' ? parseInt(value) : value
    });
  };

  const handleUpdateBooking = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentBooking) return;

    try {
      // Sử dụng API thực để cập nhật đặt phòng
      const response = await bookingService.updateBooking(currentBooking.bookingId, formData);

      if (response.success) {
        toast.success('Cập nhật đặt phòng thành công');
        fetchBookings();
        setShowModal(false);
      } else {
        throw new Error(response.message || 'Không thể cập nhật đặt phòng');
      }
    } catch (error: any) {
      console.error('Error updating booking:', error);
      toast.error(error.message || 'Không thể cập nhật đặt phòng');
    }
  };

  const handleUpdateStatus = async (id: number, status: string, paymentStatus: string) => {
    try {
      // Sử dụng API thực để cập nhật trạng thái đặt phòng
      const response = await bookingService.updateBookingStatus(id, {
        status,
        paymentStatus
      });

      if (response.success) {
        toast.success('Cập nhật trạng thái thành công');
        fetchBookings();
      } else {
        throw new Error(response.message || 'Không thể cập nhật trạng thái');
      }
    } catch (error: any) {
      console.error('Error updating booking status:', error);
      toast.error(error.message || 'Không thể cập nhật trạng thái');
    }
  };

  const handleDeleteBooking = async (id: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa đặt phòng này?')) {
      try {
        // Sử dụng API thực để xóa đặt phòng
        const response = await bookingService.deleteBooking(id);

        if (response.success) {
          toast.success('Xóa đặt phòng thành công');
          fetchBookings();
        } else {
          throw new Error(response.message || 'Không thể xóa đặt phòng');
        }
      } catch (error: any) {
        console.error('Error deleting booking:', error);
        toast.error(error.message || 'Không thể xóa đặt phòng');
      }
    }
  };

  const openEditModal = (booking: Booking) => {
    setCurrentBooking(booking);
    setFormData({
      customerId: booking.customerId,
      employeeId: booking.employeeId,
      checkInDate: new Date(booking.checkInDate).toISOString().split('T')[0],
      checkOutDate: new Date(booking.checkOutDate).toISOString().split('T')[0],
      status: booking.status,
      paymentStatus: booking.paymentStatus,
      notes: booking.notes,
      bookingSource: booking.bookingSource
    });
    setIsEditing(true);
    setShowModal(true);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN');
  };

  const filteredBookings = bookings.filter(booking => {
    const matchesSearch =
      booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.bookingId.toString().includes(searchTerm);

    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  return (
    <div>
      <div className={styles.toolbarSection}>
        <div className={styles.searchBox}>
          <FaSearch />
          <input
            type="text"
            placeholder="Tìm kiếm đặt phòng..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className={styles.filterContainer}>
          <select
            className={styles.filterSelect}
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">Tất cả trạng thái</option>
            <option value="Pending">Chờ xác nhận</option>
            <option value="Confirmed">Đã xác nhận</option>
            <option value="Checked-in">Đã nhận phòng</option>
            <option value="Checked-out">Đã trả phòng</option>
            <option value="Cancelled">Đã hủy</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Khách hàng</th>
                <th>Ngày đặt</th>
                <th>Nhận phòng</th>
                <th>Trả phòng</th>
                <th>Tổng tiền</th>
                <th>Trạng thái</th>
                <th>Thanh toán</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredBookings.length > 0 ? (
                filteredBookings.map((booking) => (
                  <tr key={booking.bookingId}>
                    <td>{booking.bookingId}</td>
                    <td>{booking.customerName}</td>
                    <td>{formatDate(booking.bookingDate)}</td>
                    <td>{formatDate(booking.checkInDate)}</td>
                    <td>{formatDate(booking.checkOutDate)}</td>
                    <td>${booking.totalAmount.toFixed(2)}</td>
                    <td>
                      <select
                        value={booking.status}
                        onChange={(e) => handleUpdateStatus(booking.bookingId, e.target.value, booking.paymentStatus)}
                        className={`${styles.statusBadge} ${styles[`status${booking.status.replace('-', '')}`]}`}
                      >
                        <option value="Pending">Chờ xác nhận</option>
                        <option value="Confirmed">Đã xác nhận</option>
                        <option value="Checked-in">Đã nhận phòng</option>
                        <option value="Checked-out">Đã trả phòng</option>
                        <option value="Cancelled">Đã hủy</option>
                      </select>
                    </td>
                    <td>
                      <select
                        value={booking.paymentStatus}
                        onChange={(e) => handleUpdateStatus(booking.bookingId, booking.status, e.target.value)}
                        className={`${styles.statusBadge} ${styles[`status${booking.paymentStatus}`]}`}
                      >
                        <option value="Unpaid">Chưa thanh toán</option>
                        <option value="Paid">Đã thanh toán</option>
                        <option value="Refunded">Đã hoàn tiền</option>
                      </select>
                    </td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.viewButton}`}
                        onClick={() => fetchBookingDetail(booking.bookingId)}
                      >
                        <FaEye />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.editButton}`}
                        onClick={() => openEditModal(booking)}
                      >
                        <FaEdit />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteBooking(booking.bookingId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={9} style={{ textAlign: 'center' }}>
                    Không tìm thấy đặt phòng nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal for editing booking */}
      {showModal && currentBooking && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2 className={styles.modalTitle}>
                Chỉnh sửa đặt phòng #{currentBooking.bookingId}
              </h2>
              <button className={styles.closeButton} onClick={() => setShowModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={handleUpdateBooking}>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Ngày nhận phòng</label>
                  <input
                    type="date"
                    name="checkInDate"
                    value={formData.checkInDate}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Ngày trả phòng</label>
                  <input
                    type="date"
                    name="checkOutDate"
                    value={formData.checkOutDate}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Trạng thái</label>
                  <select
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="Pending">Chờ xác nhận</option>
                    <option value="Confirmed">Đã xác nhận</option>
                    <option value="Checked-in">Đã nhận phòng</option>
                    <option value="Checked-out">Đã trả phòng</option>
                    <option value="Cancelled">Đã hủy</option>
                  </select>
                </div>
                <div className={styles.formGroup}>
                  <label>Trạng thái thanh toán</label>
                  <select
                    name="paymentStatus"
                    value={formData.paymentStatus}
                    onChange={handleInputChange}
                    required
                  >
                    <option value="Unpaid">Chưa thanh toán</option>
                    <option value="Paid">Đã thanh toán</option>
                    <option value="Refunded">Đã hoàn tiền</option>
                  </select>
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Nguồn đặt phòng</label>
                <select
                  name="bookingSource"
                  value={formData.bookingSource}
                  onChange={handleInputChange}
                  required
                >
                  <option value="Website">Website</option>
                  <option value="Phone">Điện thoại</option>
                  <option value="Email">Email</option>
                  <option value="Walk-in">Trực tiếp</option>
                  <option value="Agency">Đại lý</option>
                </select>
              </div>
              <div className={styles.formGroup}>
                <label>Ghi chú</label>
                <textarea
                  name="notes"
                  value={formData.notes}
                  onChange={handleInputChange}
                  rows={3}
                />
              </div>
              <div className={styles.buttonContainer}>
                <button
                  type="button"
                  className={`${styles.button} ${styles.secondaryButton}`}
                  onClick={() => setShowModal(false)}
                >
                  Hủy
                </button>
                <button type="submit" className={`${styles.button} ${styles.primaryButton}`}>
                  Cập nhật
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal for booking details */}
      {showDetailModal && currentBookingDetail && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2 className={styles.modalTitle}>
                Chi tiết đặt phòng #{currentBookingDetail.bookingId}
              </h2>
              <button className={styles.closeButton} onClick={() => setShowDetailModal(false)}>
                &times;
              </button>
            </div>
            <div className={styles.formContainer}>
              <h3 className={styles.formTitle}>Thông tin đặt phòng</h3>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Ngày đặt</label>
                  <p>{formatDate(currentBookingDetail.bookingDate)}</p>
                </div>
                <div className={styles.formGroup}>
                  <label>Nguồn đặt phòng</label>
                  <p>{currentBookingDetail.bookingSource}</p>
                </div>
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Ngày nhận phòng</label>
                  <p>{formatDate(currentBookingDetail.checkInDate)}</p>
                </div>
                <div className={styles.formGroup}>
                  <label>Ngày trả phòng</label>
                  <p>{formatDate(currentBookingDetail.checkOutDate)}</p>
                </div>
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Trạng thái</label>
                  <p>{currentBookingDetail.bookingStatus}</p>
                </div>
                <div className={styles.formGroup}>
                  <label>Trạng thái thanh toán</label>
                  <p>{currentBookingDetail.paymentStatus}</p>
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Tổng tiền</label>
                <p>${currentBookingDetail.totalAmount.toFixed(2)}</p>
              </div>
              <div className={styles.formGroup}>
                <label>Ghi chú</label>
                <p>{currentBookingDetail.notes || 'Không có ghi chú'}</p>
              </div>
            </div>

            <div className={styles.formContainer}>
              <h3 className={styles.formTitle}>Thông tin phòng</h3>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Loại phòng</label>
                  <p>{currentBookingDetail.name}</p>
                </div>
                <div className={styles.formGroup}>
                  <label>Số lượng</label>
                  <p>{currentBookingDetail.roomTypeQuantity}</p>
                </div>
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Giá phòng</label>
                  <p>${currentBookingDetail.roomTypePrice.toFixed(2)}</p>
                </div>
                {currentBookingDetail.roomNumber && (
                  <div className={styles.formGroup}>
                    <label>Số phòng</label>
                    <p>{currentBookingDetail.roomNumber}</p>
                  </div>
                )}
              </div>
              {currentBookingDetail.roomStatus && (
                <div className={styles.formGroup}>
                  <label>Trạng thái phòng</label>
                  <p>{currentBookingDetail.roomStatus}</p>
                </div>
              )}
            </div>

            <div className={styles.buttonContainer}>
              <button
                className={`${styles.button} ${styles.secondaryButton}`}
                onClick={() => setShowDetailModal(false)}
              >
                Đóng
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingManagement;
