.pageContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.bookingHeader, .bookingDetailHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.bookingHeader h1, .bookingDetailHeader h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.bookingHeader p {
  font-size: 1.1rem;
  color: #666;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3498db;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.emptyState {
  text-align: center;
  padding: 3rem;
  background-color: #f9f9f9;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.emptyState h2 {
  font-size: 1.8rem;
  color: #333;
  margin-bottom: 1rem;
}

.emptyState p {
  font-size: 1.1rem;
  color: #666;
  margin-bottom: 2rem;
}

.bookNowButton {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.8rem 1.5rem;
  font-size: 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.bookNowButton:hover {
  background-color: #2980b9;
}

.bookingList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.bookingCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s, box-shadow 0.3s;
}

.bookingCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.bookingHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.bookingHeader h3 {
  font-size: 1.3rem;
  color: #333;
  margin: 0;
}

.statusBadge {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.statusConfirmed {
  background-color: #2ecc71;
  color: white;
}

.statusPending {
  background-color: #f39c12;
  color: white;
}

.statusCancelled {
  background-color: #e74c3c;
  color: white;
}

.statusCompleted {
  background-color: #3498db;
  color: white;
}

.bookingDetails {
  padding: 1.5rem;
}

.detailRow {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.8rem;
}

.detailLabel {
  font-weight: 600;
  color: #666;
}

.detailValue {
  color: #333;
}

.bookingActions {
  display: flex;
  justify-content: space-between;
  padding: 1.5rem;
  border-top: 1px solid #eee;
}

.viewDetailsButton, .cancelButton {
  padding: 0.6rem 1.2rem;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.viewDetailsButton {
  background-color: #3498db;
  color: white;
  border: none;
}

.viewDetailsButton:hover {
  background-color: #2980b9;
}

.cancelButton {
  background-color: white;
  color: #e74c3c;
  border: 1px solid #e74c3c;
}

.cancelButton:hover {
  background-color: #e74c3c;
  color: white;
}

/* Chi tiết đặt phòng */
.bookingDetailHeader {
  text-align: center;
  margin-bottom: 2rem;
}

.bookingDetailHeader h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 0.5rem;
}

.bookingDetailHeader p {
  font-size: 1.1rem;
  color: #666;
}

.backButton {
  background-color: transparent;
  border: none;
  color: #3498db;
  font-size: 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.backButton:hover {
  text-decoration: underline;
}

.bookingDetailCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 2rem;
}

.bookingDetailSection {
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.bookingDetailSection:last-child {
  border-bottom: none;
}

.bookingDetailSection h2 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1.5rem;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.bookingInfo {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.bookingInfoItem {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.label {
  font-weight: 600;
  color: #666;
  margin-bottom: 0.3rem;
}

.value {
  color: #333;
}

.roomInfo {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
}

.roomImageContainer {
  flex: 0 0 300px;
  position: relative;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
}

.roomImage {
  object-fit: cover;
  border-radius: 8px;
}

.roomDetails {
  flex: 1;
  min-width: 250px;
}

.roomDetails h3 {
  font-size: 1.5rem;
  color: #333;
  margin-bottom: 1rem;
}

.roomInfoItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.8rem;
}

.servicesTable {
  width: 100%;
  overflow-x: auto;
}

.servicesTable table {
  width: 100%;
  border-collapse: collapse;
}

.servicesTable th, .servicesTable td {
  padding: 0.8rem;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.servicesTable th {
  font-weight: 600;
  color: #333;
  background-color: #f9f9f9;
}

.servicesTable tr:hover {
  background-color: #f9f9f9;
}

.deleteButton {
  background-color: transparent;
  color: #e74c3c;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  transition: color 0.3s;
}

.deleteButton:hover {
  color: #c0392b;
}

.addServiceButton {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 0.6rem 1.2rem;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.addServiceButton:hover {
  background-color: #2980b9;
}

.emptyServices {
  text-align: center;
  padding: 2rem;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.paymentSummary {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 1.5rem;
}

.paymentItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.totalAmount {
  font-weight: 600;
  font-size: 1.3rem;
  border-top: 1px solid #ddd;
  padding-top: 1rem;
  margin-top: 1rem;
}

/* Modal */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.modalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #eee;
}

.modalHeader h2 {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
}

.closeButton {
  background-color: transparent;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
}

.modalBody {
  padding: 1.5rem;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.formGroup label {
  display: block;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #333;
}

.formGroup input, .formGroup select {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.formGroup select option {
  background-color: #121212;
  color: white;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #eee;
}

.cancelButton, .confirmButton {
  padding: 0.8rem 1.5rem;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.cancelButton {
  background-color: white;
  color: #333;
  border: 1px solid #ddd;
}

.cancelButton:hover {
  background-color: #f5f5f5;
}

.confirmButton {
  background-color: #3498db;
  color: white;
  border: none;
}

.confirmButton:hover {
  background-color: #2980b9;
}

.errorContainer {
  text-align: center;
  padding: 3rem;
  max-width: 600px;
  margin: 0 auto;
}

.errorContainer h2 {
  font-size: 1.8rem;
  color: #e74c3c;
  margin-bottom: 1.5rem;
}
