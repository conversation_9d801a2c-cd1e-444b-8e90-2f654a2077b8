import React, { useState, useEffect } from 'react';
import { roomTypeApi } from '../../services/adminService';
import styles from '../../styles/admin.module.css';
import { FaEdit, FaTrash, FaPlus, FaSearch } from 'react-icons/fa';
import { toast } from 'react-toastify';

interface RoomType {
  roomTypeId: number;
  name: string;
  description: string;
  basePrice: number;
  capacity: number;
  bedType: string;
  amenities: string;
  isActive: boolean;
}

const RoomTypeManagement: React.FC = () => {
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [showModal, setShowModal] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [currentRoomType, setCurrentRoomType] = useState<RoomType | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    basePrice: 0,
    capacity: 1,
    bedType: '',
    amenities: '',
    isActive: true
  });

  useEffect(() => {
    fetchRoomTypes();
  }, []);

  const fetchRoomTypes = async () => {
    try {
      setLoading(true);
      const response = await roomTypeApi.getAllRoomTypes();

      // Kiểm tra cấu trúc response và truy cập đúng thuộc tính data
      if (response && response.success && Array.isArray(response.data)) {
        setRoomTypes(response.data);
      } else if (Array.isArray(response)) {
        // Trường hợp response là mảng trực tiếp
        setRoomTypes(response);
      } else {
        console.error('Unexpected response format:', response);
        toast.error('Định dạng dữ liệu không hợp lệ');
        setRoomTypes([]); // Đặt mảng rỗng để tránh lỗi
      }
    } catch (error: any) {
      console.error('Error fetching room types:', error);
      toast.error(error.message || 'Không thể tải danh sách loại phòng');
      setRoomTypes([]); // Đặt mảng rỗng để tránh lỗi
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setFormData({
      ...formData,
      [name]: type === 'checkbox'
        ? (e.target as HTMLInputElement).checked
        : type === 'number'
          ? parseFloat(value)
          : value
    });
  };

  const handleAddRoomType = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const response = await roomTypeApi.addRoomType(formData);

      if (response && response.success) {
        toast.success(response.message || 'Thêm loại phòng thành công');
        fetchRoomTypes();
        setShowModal(false);
        resetForm();
      } else {
        toast.error(response?.message || 'Thêm loại phòng thất bại');
      }
    } catch (error: any) {
      console.error('Error adding room type:', error);
      toast.error(error.message || 'Thêm loại phòng thất bại');
    }
  };

  const handleUpdateRoomType = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentRoomType) return;

    try {
      const response = await roomTypeApi.updateRoomType(currentRoomType.roomTypeId, formData);

      if (response && response.success) {
        toast.success(response.message || 'Cập nhật loại phòng thành công');
        fetchRoomTypes();
        setShowModal(false);
        resetForm();
      } else {
        toast.error(response?.message || 'Cập nhật loại phòng thất bại');
      }
    } catch (error: any) {
      console.error('Error updating room type:', error);
      toast.error(error.message || 'Cập nhật loại phòng thất bại');
    }
  };

  const handleDeleteRoomType = async (id: number) => {
    if (window.confirm('Bạn có chắc chắn muốn xóa loại phòng này?')) {
      try {
        const response = await roomTypeApi.deleteRoomType(id);

        if (response && response.success) {
          toast.success(response.message || 'Xóa loại phòng thành công');
          fetchRoomTypes();
        } else {
          toast.error(response?.message || 'Xóa loại phòng thất bại');
        }
      } catch (error: any) {
        console.error('Error deleting room type:', error);
        toast.error(error.message || 'Xóa loại phòng thất bại');
      }
    }
  };

  const openEditModal = (roomType: RoomType) => {
    setCurrentRoomType(roomType);
    setFormData({
      name: roomType.name,
      description: roomType.description,
      basePrice: roomType.basePrice,
      capacity: roomType.capacity,
      bedType: roomType.bedType,
      amenities: roomType.amenities,
      isActive: roomType.isActive
    });
    setIsEditing(true);
    setShowModal(true);
  };

  const openAddModal = () => {
    resetForm();
    setIsEditing(false);
    setShowModal(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      basePrice: 0,
      capacity: 1,
      bedType: '',
      amenities: '',
      isActive: true
    });
    setCurrentRoomType(null);
  };

  const filteredRoomTypes = roomTypes && roomTypes.length > 0
    ? roomTypes.filter(roomType =>
        (roomType.name && roomType.name.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (roomType.description && roomType.description.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    : [];

  return (
    <div>
      <div className={styles.toolbarSection}>
        <div className={styles.searchBox}>
          <FaSearch />
          <input
            type="text"
            placeholder="Tìm kiếm loại phòng..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <button className={`${styles.button} ${styles.primaryButton}`} onClick={openAddModal}>
          <FaPlus /> Thêm loại phòng
        </button>
      </div>

      {loading ? (
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
        </div>
      ) : (
        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Tên</th>
                <th>Mô tả</th>
                <th>Giá cơ bản</th>
                <th>Sức chứa</th>
                <th>Loại giường</th>
                <th>Tiện nghi</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredRoomTypes && filteredRoomTypes.length > 0 ? (
                filteredRoomTypes.map((roomType) => (
                  <tr key={roomType.roomTypeId}>
                    <td>{roomType.roomTypeId}</td>
                    <td>{roomType.name}</td>
                    <td>{roomType.description && roomType.description.length > 30
                      ? `${roomType.description.substring(0, 30)}...`
                      : roomType.description || ''}
                    </td>
                    <td>${roomType.basePrice}</td>
                    <td>{roomType.capacity}</td>
                    <td>{roomType.bedType}</td>
                    <td>{roomType.amenities && roomType.amenities.length > 30
                      ? `${roomType.amenities.substring(0, 30)}...`
                      : roomType.amenities || ''}
                    </td>
                    <td>
                      <span className={`${styles.statusBadge} ${roomType.isActive ? styles.statusAvailable : styles.statusBooked}`}>
                        {roomType.isActive ? 'Hoạt động' : 'Không hoạt động'}
                      </span>
                    </td>
                    <td>
                      <button
                        className={`${styles.actionButton} ${styles.editButton}`}
                        onClick={() => openEditModal(roomType)}
                      >
                        <FaEdit />
                      </button>
                      <button
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        onClick={() => handleDeleteRoomType(roomType.roomTypeId)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={9} style={{ textAlign: 'center' }}>
                    Không tìm thấy loại phòng nào
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      )}

      {/* Modal for adding/editing room type */}
      {showModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h2 className={styles.modalTitle}>
                {isEditing ? 'Chỉnh sửa loại phòng' : 'Thêm loại phòng mới'}
              </h2>
              <button className={styles.closeButton} onClick={() => setShowModal(false)}>
                &times;
              </button>
            </div>
            <form onSubmit={isEditing ? handleUpdateRoomType : handleAddRoomType}>
              <div className={styles.formGroup}>
                <label>Tên loại phòng</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Mô tả</label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  required
                />
              </div>
              <div className={styles.formRow}>
                <div className={styles.formGroup}>
                  <label>Giá cơ bản</label>
                  <input
                    type="number"
                    name="basePrice"
                    min="0"
                    step="0.01"
                    value={formData.basePrice}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Sức chứa</label>
                  <input
                    type="number"
                    name="capacity"
                    min="1"
                    value={formData.capacity}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
              <div className={styles.formGroup}>
                <label>Loại giường</label>
                <input
                  type="text"
                  name="bedType"
                  value={formData.bedType}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>Tiện nghi</label>
                <textarea
                  name="amenities"
                  value={formData.amenities}
                  onChange={handleInputChange}
                  rows={3}
                  required
                />
              </div>
              <div className={styles.formGroup}>
                <label>
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                  />
                  Đang hoạt động
                </label>
              </div>
              <div className={styles.buttonContainer}>
                <button
                  type="button"
                  className={`${styles.button} ${styles.secondaryButton}`}
                  onClick={() => setShowModal(false)}
                >
                  Hủy
                </button>
                <button type="submit" className={`${styles.button} ${styles.primaryButton}`}>
                  {isEditing ? 'Cập nhật' : 'Thêm mới'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoomTypeManagement;
