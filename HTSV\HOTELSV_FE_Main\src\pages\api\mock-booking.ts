import { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Chỉ cho phép phương thức POST
  if (req.method !== 'POST') {
    return res.status(405).json({ success: false, message: 'Method Not Allowed' });
  }

  try {
    // Lấy dữ liệu từ request body
    const bookingData = req.body;

    // Tạo một booking ID ngẫu nhiên
    const bookingId = Math.floor(Math.random() * 1000) + 10;

    // Tạo dữ liệu giả cho đặt phòng thành công
    const mockResponse = {
      success: true,
      message: 'Đặt phòng thành công (mock data)',
      data: {
        bookingId: bookingId,
        customerId: bookingData.CustomerId || 1,
        customerName: 'Khách hàng mẫu',
        checkInDate: bookingData.CheckInDate,
        checkOutDate: bookingData.CheckOutDate,
        roomType: 'Phòng Deluxe',
        totalAmount: 1500000,
        status: 'Confirmed',
        bookingDate: new Date().toISOString(),
        createdAt: new Date().toISOString()
      }
    };

    // Tr<PERSON> về kết quả
    return res.status(200).json(mockResponse);
  } catch (error: any) {
    console.error('Mock booking API error:', error);
    return res.status(500).json({
      success: false,
      message: error.message || 'Internal Server Error'
    });
  }
}
