:root {
  --primary-color: #C0A080;
  --secondary-color: #D4AF37;
  --accent-color: #E74C3C;
  --background-color: #0a0a0a;
  --text-color: #E0E0E0;
  --card-bg: #121212;
  --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  --border-radius: 12px;
  --border-color: #1a1a1a;
  --dark-border: #2a2a2a;
  --darker-bg: #0f0f0f;
}

.navbar {
  background: rgba(18, 18, 18, 1);
  backdrop-filter: blur(10px);
  margin: 0;
  padding: 0.8rem 2rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: fixed;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 1000;
  box-sizing: border-box;
}

.navbar.scrolled {
  background: rgba(18, 18, 18, 1);
  backdrop-filter: blur(15px);
  padding: 0.8rem 2rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  height: 70px;
}

.navbar.scrolled .navLink {
  color: var(--text-color);
}

.navbar.scrolled .brandText {
  color: var(--primary-color);
}

.container {
  margin: 0 auto;
  padding: 0 2rem;
  width: 100%;
  box-sizing: border-box;
  max-width: 1400px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Phần trái - Logo */
.navbarLeft {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
}

.navbarBrand {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.navbarBrand:hover {
  transform: translateY(-1px);
}

.navLogo {
  height: 40px;
  width: auto;
  margin-right: 10px;
  object-fit: contain;
}

.brandText {
  color: #ffffff;
  font-weight: 700;
  font-size: 1.3rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.brandText:hover {
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

/* Phần giữa - Menu chính */
.navLinksContainer {
  flex: 1 1 auto;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mainMenu {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: nowrap;
}

.navLink {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
  padding: 0.5rem 0.8rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  white-space: nowrap;
}

.navLink:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background: var(--secondary-color);
  transition: width 0.3s ease;
}

.navLink:hover {
  color: rgba(255, 255, 255, 1);
}

.navLink:hover:after,
.navLink.active:after {
  width: 70%;
  background: var(--primary-color);
}

/* Phần phải - Các nút */
.navActions {
  flex: 0 0 auto;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  white-space: nowrap;
}

.userName {
  padding: 0.4rem 0.8rem;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
}

.logoutButton {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  box-shadow: 0 0 15px rgba(248, 113, 113, 0.1);
}

.logoutButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.loginButton,
.registerButton,
.bookingsButton,
.adminButton {
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  white-space: nowrap;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-decoration: none;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
}

.loginButton:hover,
.registerButton:hover,
.bookingsButton:hover,
.adminButton:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.registerButton {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.2);
}

.registerButton:hover {
  box-shadow: 0 0 25px rgba(59, 130, 246, 0.3);
}

.loginButton {
  box-shadow: 0 0 15px rgba(96, 165, 250, 0.1);
}

.loginButton:hover {
  box-shadow: 0 0 20px rgba(96, 165, 250, 0.2);
}

.bookingsButton {
  box-shadow: 0 0 15px rgba(192, 160, 128, 0.2);
}

.bookingsButton:hover {
  box-shadow: 0 0 25px rgba(192, 160, 128, 0.3);
}

.adminButton {
  box-shadow: 0 0 15px rgba(231, 76, 60, 0.2);
  background: rgba(231, 76, 60, 0.2);
  border: 1px solid rgba(231, 76, 60, 0.3);
}

.adminButton:hover {
  box-shadow: 0 0 25px rgba(231, 76, 60, 0.3);
  background: rgba(231, 76, 60, 0.3);
}

.loginButton:active,
.registerButton:active,
.logoutButton:active,
.bookingsButton:active,
.adminButton:active {
  transform: translateY(0);
  opacity: 0.9;
}

.pageContainer {
  padding-top: 100px;
  margin: 0;
  min-height: 100vh;
  background: #121212;
  color: #fff;
  padding: 2rem;
}

.roomsHeader {
  text-align: center;
  margin-bottom: 2rem;
  padding-top: 6rem;
}

.roomsHeader h1 {
  font-size: 2.5rem;
  color: var(--text-color);
  margin-bottom: 1rem;
}

.roomsHeader p {
  font-size: 1.2rem;
  color: var(--text-color);
  opacity: 0.8;
}

.filterTabs {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 2rem;
}

.filterTab {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.filterTab:hover {
  background: rgba(255, 255, 255, 0.2);
}

.filterTab.active {
  background: rgba(192, 160, 128, 0.3);
  border-color: rgba(192, 160, 128, 0.5);
  color: white;
}

.roomsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  padding: 1rem;
}

.roomCard {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  transition: transform 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.roomCard:hover {
  transform: translateY(-5px);
}

.roomImageContainer {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-bottom: 1px solid var(--border-dark);
}

.roomImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.roomOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.roomCard:hover .roomOverlay {
  opacity: 1;
}

.roomContent {
  background: rgba(15, 15, 15, 0.5);
  padding: 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.roomName {
  font-size: 1.25rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.roomFeatures {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex: 1;
}

.feature {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
}

.roomFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.priceInfo {
  display: flex;
  flex-direction: column;
}

.priceLabel {
  font-size: 0.9rem;
  color: var(--text-color);
}

.priceValue {
  font-size: 1.25rem;
  color: var(--secondary-color);
  font-weight: bold;
}

.bookNowButton {
  background: rgba(192, 160, 128, 0.3);
  color: white;
  border: 1px solid rgba(192, 160, 128, 0.5);
  padding: 0.8rem 1.5rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.bookNowButton:hover {
  background: rgba(192, 160, 128, 0.5);
}

.roomActions {
  display: flex;
  gap: 0.5rem;
}

.detailButton {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.6rem 1rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  font-size: 0.9rem;
}

.detailButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.bookButton {
  background: rgba(192, 160, 128, 0.3);
  color: white;
  border: 1px solid rgba(192, 160, 128, 0.5);
  padding: 0.6rem 1rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  font-size: 0.9rem;
}

.bookButton:hover {
  background: rgba(192, 160, 128, 0.5);
}

.authContainer {
  padding-top: 80px; /* Add space for fixed navbar */
}

/* Room Detail Page Styles */
.roomDetailContainer {
  padding-top: 100px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: var(--text-color);
}

.roomDetailHeader {
  display: flex;
  align-items: center;
  margin-bottom: 2rem;
  padding-top: 5rem;
}

.roomDetailHeader h1 {
  margin-left: 1rem;
  font-size: 2rem;
  color: var(--primary-color);
}

.backButton {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  padding: 0.6rem 1rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  font-size: 0.9rem;
}

.backButton:hover {
  background: rgba(255, 255, 255, 0.2);
}

.roomDetailContent {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.roomDetailImageContainer {
  position: relative;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
}

.roomDetailImage {
  width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

.roomDetailInfo {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.roomDetailSection {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
  padding: 1.5rem;
}

.roomDetailSection h2 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.roomDetailGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.roomDetailItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.roomDetailLabel {
  color: var(--text-color);
  opacity: 0.7;
  font-size: 0.9rem;
}

.roomDetailValue {
  color: var(--text-color);
  font-size: 1.1rem;
}

.roomDescription {
  line-height: 1.6;
  color: var(--text-color);
  opacity: 0.9;
}

.amenitiesList {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
}

.amenityItem {
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
}

.loading, .error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  font-size: 1.2rem;
  color: var(--text-color);
}

.error {
  color: var(--accent-color);
}

@media (max-width: 1024px) {
  .navLinksContainer {
    gap: 1rem;
  }

  .navLink {
    padding: 0.5rem;
  }
}

@media (max-width: 768px) {
  .pageContainer {
    padding: 1rem;
  }

  .roomsHeader h1 {
    font-size: 2rem;
  }

  .filterTabs {
    flex-wrap: wrap;
  }

  .roomsGrid {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 1rem;
  }

  .brandText {
    display: none;
  }

  .navLinksContainer {
    display: none;
  }

  .navActions {
    gap: 0.5rem;
  }

  .loginButton,
  .registerButton,
  .logoutButton,
  .bookingsButton {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }

  .navLogo {
    height: 40px;
  }

  /* Room Detail Page Responsive */
  .roomDetailContent {
    grid-template-columns: 1fr;
  }

  .roomDetailContainer {
    padding: 1rem;
  }

  .roomDetailHeader {
    padding-top: 6rem;
  }

  .roomDetailGrid {
    grid-template-columns: 1fr;
  }
}