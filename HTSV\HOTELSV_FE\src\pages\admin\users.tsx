import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../../components/Navbar';
import { useAuth } from '../../contexts/AuthContext';
import styles from '../../styles/admin.module.css';
import { toast } from 'react-toastify';
import Head from 'next/head';
import { FaEdit, FaTrash, FaPlus, FaSearch } from 'react-icons/fa';

interface User {
  id: number;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  role: string;
  isActive: boolean;
  createdAt: string;
}

export default function UsersManagement() {
  const router = useRouter();
  const { isAuthenticated, user, loading: authLoading } = useAuth();
  
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    firstName: '',
    lastName: '',
    phone: '',
    role: 'User',
    password: '',
    confirmPassword: ''
  });

  useEffect(() => {
    // Kiểm tra xác thực
    if (!authLoading && !isAuthenticated) {
      toast.info('Vui lòng đăng nhập để truy cập trang quản lý');
      router.push('/auth/login?redirect=/admin/users');
      return;
    }

    // Trong thực tế, nên kiểm tra quyền admin
    // if (user?.role !== 'Admin') {
    //   toast.error('Bạn không có quyền truy cập trang này');
    //   router.push('/');
    //   return;
    // }

    // Nếu đã xác thực, tải danh sách người dùng
    fetchUsers();
  }, [isAuthenticated, authLoading, router]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      
      // Trong thực tế, nên có API để lấy danh sách người dùng
      // Ở đây, chúng ta sẽ sử dụng dữ liệu mẫu
      
      // Giả lập delay để mô phỏng việc gọi API
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const mockUsers: User[] = [
        {
          id: 1,
          username: 'admin',
          email: '<EMAIL>',
          firstName: 'Admin',
          lastName: 'User',
          phone: '0123456789',
          role: 'Admin',
          isActive: true,
          createdAt: '2023-01-01T00:00:00Z'
        },
        {
          id: 2,
          username: 'user1',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'One',
          phone: '0123456788',
          role: 'User',
          isActive: true,
          createdAt: '2023-01-02T00:00:00Z'
        },
        {
          id: 3,
          username: 'user2',
          email: '<EMAIL>',
          firstName: 'User',
          lastName: 'Two',
          phone: '0123456787',
          role: 'User',
          isActive: false,
          createdAt: '2023-01-03T00:00:00Z'
        },
        {
          id: 4,
          username: 'staff1',
          email: '<EMAIL>',
          firstName: 'Staff',
          lastName: 'One',
          phone: '0123456786',
          role: 'Staff',
          isActive: true,
          createdAt: '2023-01-04T00:00:00Z'
        }
      ];
      
      setUsers(mockUsers);
    } catch (error: any) {
      console.error('Error fetching users:', error);
      setError(error.message || 'Không thể tải danh sách người dùng');
      toast.error('Không thể tải danh sách người dùng');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddUser = () => {
    setSelectedUser(null);
    setFormData({
      username: '',
      email: '',
      firstName: '',
      lastName: '',
      phone: '',
      role: 'User',
      password: '',
      confirmPassword: ''
    });
    setIsModalOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setFormData({
      username: user.username,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      phone: user.phone,
      role: user.role,
      password: '',
      confirmPassword: ''
    });
    setIsModalOpen(true);
  };

  const handleDeleteUser = async (userId: number) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa người dùng này?')) {
      return;
    }

    try {
      // Trong thực tế, nên có API để xóa người dùng
      // Ở đây, chúng ta sẽ giả lập thành công và xóa người dùng khỏi state
      
      // Xóa người dùng khỏi state
      setUsers(prev => prev.filter(user => user.id !== userId));
      
      toast.success('Xóa người dùng thành công!');
    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast.error(error.message || 'Xóa người dùng thất bại!');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Kiểm tra dữ liệu
    if (!formData.username || !formData.email) {
      toast.error('Vui lòng nhập tên đăng nhập và email');
      return;
    }
    
    if (!selectedUser && (!formData.password || formData.password.length < 6)) {
      toast.error('Mật khẩu phải có ít nhất 6 ký tự');
      return;
    }
    
    if (!selectedUser && formData.password !== formData.confirmPassword) {
      toast.error('Mật khẩu xác nhận không khớp');
      return;
    }
    
    try {
      if (selectedUser) {
        // Cập nhật người dùng
        // Trong thực tế, nên có API để cập nhật người dùng
        // Ở đây, chúng ta sẽ giả lập thành công và cập nhật người dùng trong state
        
        setUsers(prev => prev.map(user => 
          user.id === selectedUser.id 
            ? { 
                ...user, 
                username: formData.username,
                email: formData.email,
                firstName: formData.firstName,
                lastName: formData.lastName,
                phone: formData.phone,
                role: formData.role
              } 
            : user
        ));
        
        toast.success('Cập nhật người dùng thành công!');
      } else {
        // Thêm người dùng mới
        // Trong thực tế, nên có API để thêm người dùng
        // Ở đây, chúng ta sẽ giả lập thành công và thêm người dùng vào state
        
        const newUser: User = {
          id: Math.max(...users.map(u => u.id)) + 1,
          username: formData.username,
          email: formData.email,
          firstName: formData.firstName,
          lastName: formData.lastName,
          phone: formData.phone,
          role: formData.role,
          isActive: true,
          createdAt: new Date().toISOString()
        };
        
        setUsers(prev => [...prev, newUser]);
        
        toast.success('Thêm người dùng thành công!');
      }
      
      setIsModalOpen(false);
    } catch (error: any) {
      console.error('Error saving user:', error);
      toast.error(error.message || 'Lưu người dùng thất bại!');
    }
  };

  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('vi-VN', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (authLoading || loading) {
    return (
      <>
        <Head>
          <title>Quản lý người dùng | Hotel Nhóm 1</title>
        </Head>
        <Navbar />
        <div className={styles.adminContainer}>
          <div className={styles.loading}>Đang tải...</div>
        </div>
      </>
    );
  }

  return (
    <>
      <Head>
        <title>Quản lý người dùng | Hotel Nhóm 1</title>
      </Head>
      <Navbar />
      <div className={styles.adminContainer}>
        <div className={styles.adminHeader}>
          <h1>Quản lý người dùng</h1>
          <button 
            className={styles.addButton}
            onClick={handleAddUser}
          >
            <FaPlus /> Thêm người dùng
          </button>
        </div>

        <div className={styles.searchContainer}>
          <div className={styles.searchBox}>
            <FaSearch className={styles.searchIcon} />
            <input
              type="text"
              placeholder="Tìm kiếm người dùng..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
        </div>

        {error && <div className={styles.error}>{error}</div>}

        <div className={styles.tableContainer}>
          <table className={styles.dataTable}>
            <thead>
              <tr>
                <th>ID</th>
                <th>Tên đăng nhập</th>
                <th>Email</th>
                <th>Họ tên</th>
                <th>Số điện thoại</th>
                <th>Vai trò</th>
                <th>Trạng thái</th>
                <th>Ngày tạo</th>
                <th>Thao tác</th>
              </tr>
            </thead>
            <tbody>
              {filteredUsers.length > 0 ? (
                filteredUsers.map(user => (
                  <tr key={user.id}>
                    <td>{user.id}</td>
                    <td>{user.username}</td>
                    <td>{user.email}</td>
                    <td>{`${user.firstName} ${user.lastName}`}</td>
                    <td>{user.phone}</td>
                    <td>
                      <span className={`${styles.roleBadge} ${styles[user.role.toLowerCase()]}`}>
                        {user.role}
                      </span>
                    </td>
                    <td>
                      <span className={`${styles.statusBadge} ${user.isActive ? styles.active : styles.inactive}`}>
                        {user.isActive ? 'Hoạt động' : 'Không hoạt động'}
                      </span>
                    </td>
                    <td>{formatDate(user.createdAt)}</td>
                    <td className={styles.actions}>
                      <button 
                        className={styles.editButton}
                        onClick={() => handleEditUser(user)}
                      >
                        <FaEdit />
                      </button>
                      <button 
                        className={styles.deleteButton}
                        onClick={() => handleDeleteUser(user.id)}
                      >
                        <FaTrash />
                      </button>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={9} className={styles.noData}>
                    {searchTerm ? 'Không tìm thấy người dùng phù hợp' : 'Chưa có người dùng nào'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal thêm/sửa người dùng */}
      {isModalOpen && (
        <div className={styles.modalOverlay}>
          <div className={styles.modal}>
            <div className={styles.modalHeader}>
              <h3>{selectedUser ? 'Cập nhật người dùng' : 'Thêm người dùng mới'}</h3>
              <button className={styles.closeButton} onClick={() => setIsModalOpen(false)}>×</button>
            </div>
            <form onSubmit={handleSubmit}>
              <div className={styles.formGrid}>
                <div className={styles.formGroup}>
                  <label>Tên đăng nhập</label>
                  <input
                    type="text"
                    name="username"
                    value={formData.username}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Email</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Họ</label>
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Tên</label>
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Số điện thoại</label>
                  <input
                    type="text"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                </div>
                <div className={styles.formGroup}>
                  <label>Vai trò</label>
                  <select
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                  >
                    <option value="User">User</option>
                    <option value="Staff">Staff</option>
                    <option value="Admin">Admin</option>
                  </select>
                </div>
                {!selectedUser && (
                  <>
                    <div className={styles.formGroup}>
                      <label>Mật khẩu</label>
                      <input
                        type="password"
                        name="password"
                        value={formData.password}
                        onChange={handleInputChange}
                        required={!selectedUser}
                        minLength={6}
                      />
                    </div>
                    <div className={styles.formGroup}>
                      <label>Xác nhận mật khẩu</label>
                      <input
                        type="password"
                        name="confirmPassword"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        required={!selectedUser}
                        minLength={6}
                      />
                    </div>
                  </>
                )}
              </div>
              <div className={styles.modalFooter}>
                <button type="button" className={styles.cancelButton} onClick={() => setIsModalOpen(false)}>
                  Hủy
                </button>
                <button type="submit" className={styles.submitButton}>
                  {selectedUser ? 'Cập nhật' : 'Thêm mới'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  );
}
