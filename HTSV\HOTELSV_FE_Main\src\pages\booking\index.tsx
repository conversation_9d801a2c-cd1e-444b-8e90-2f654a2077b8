import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import Navbar from '../../components/Navbar';
import styles from '../../styles/booking.module.css';
import { toast } from 'react-toastify';
import { bookingService } from '../../services/apiService';
import { format } from 'date-fns';

interface Booking {
  bookingId: number;
  customerId: number;
  customerName: string;
  checkInDate: string;
  checkOutDate: string;
  roomType: string;
  totalAmount: number;
  status: string;
  createdAt: string;
}

export default function BookingPage() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const { isAuthenticated, user } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isAuthenticated) {
      toast.error('Vui lòng đăng nhập để xem đặt phòng của bạn');
      router.push('/auth/login');
      return;
    }

    fetchBookings();
  }, [isAuthenticated, router]);

  const fetchBookings = async () => {
    try {
      setLoading(true);
      console.log('Fetching bookings...');

      // Log thông tin user để debug
      console.log('User info:', user);

      // Lấy userId từ user object
      let userId;
      if (user) {
        if (user.userId) {
          userId = user.userId;
        } else if (user.id) {
          userId = user.id;
        } else if (user.UserId) {
          userId = user.UserId;
        } else {
          // Tìm kiếm trong các thuộc tính của user
          for (const key in user) {
            if (key.toLowerCase().includes('userid') || key.toLowerCase().includes('id')) {
              userId = user[key];
              console.log(`Tìm thấy ID trong trường ${key}:`, userId);
              break;
            }
          }
        }
      }

      // Nếu không tìm thấy userId, sử dụng giá trị mặc định (chỉ cho mục đích test)
      if (!userId) {
        console.warn('Không tìm thấy userId trong thông tin user, sử dụng ID mặc định');
        userId = 1; // ID mặc định cho mục đích test
      }

      console.log('User ID được sử dụng:', userId);

      // Lấy danh sách đặt phòng theo userId
      console.log('Getting bookings by user ID...');
      const response = await bookingService.getBookingsByUser(userId);
      console.log('Booking response:', response);

      if (response && response.success && Array.isArray(response.data)) {
        console.log('Bookings data:', response.data);

        if (response.data.length === 0) {
          console.log('No bookings found');
          setBookings([]);
          return;
        }

        // Lấy thông tin user để hiển thị
        const userFullName = user?.firstName && user?.lastName
          ? `${user.firstName} ${user.lastName}`
          : user?.fullName || '';

        console.log('User full name:', userFullName);

        // Chuyển đổi dữ liệu từ API sang định dạng Booking
        const formattedBookings = response.data.map((booking: any) => ({
          bookingId: booking.bookingId || booking.BookingId,
          customerId: booking.customerId || booking.CustomerId,
          customerName: booking.customerName || booking.CustomerName || userFullName || 'Khách hàng',
          checkInDate: booking.checkInDate || booking.CheckInDate,
          checkOutDate: booking.checkOutDate || booking.CheckOutDate,
          roomType: booking.roomType || booking.RoomType || booking.name || booking.Name || 'Phòng khách sạn',
          totalAmount: booking.totalAmount || booking.TotalAmount || 0,
          status: booking.status || booking.Status || booking.bookingStatus || booking.BookingStatus || 'Pending',
          createdAt: booking.bookingDate || booking.BookingDate || booking.createdAt || booking.CreatedAt || new Date().toISOString()
        }));

        console.log('Formatted bookings:', formattedBookings);
        setBookings(formattedBookings);
      } else {
        console.log('API response format is not as expected');
        toast.error('Định dạng dữ liệu không đúng');
        setBookings([]);
      }
    } catch (error: any) {
      console.error('Error fetching bookings:', error);

      // Xử lý lỗi chi tiết hơn
      if (error.response && error.response.data) {
        console.error('API response error:', error.response.data);
        toast.error(error.response.data.message || 'Không thể tải danh sách đặt phòng');
      } else {
        toast.error(error.message || 'Không thể tải danh sách đặt phòng');
      }

      // Hiển thị trạng thái trống thay vì lỗi
      setBookings([]);
    } finally {
      setLoading(false);
    }
  };

  const cancelBooking = async (bookingId: number) => {
    if (!window.confirm('Bạn có chắc chắn muốn hủy đặt phòng này?')) {
      return;
    }

    try {
      console.log('Cancelling booking:', bookingId);

      // Sử dụng API service để hủy đặt phòng
      const response = await bookingService.deleteBooking(bookingId);
      console.log('Cancel response:', response);

      if (response && response.success) {
        toast.success('Hủy đặt phòng thành công');

        // Cập nhật UI trực tiếp thay vì gọi lại API
        setBookings(prevBookings =>
          prevBookings.map(booking =>
            booking.bookingId === bookingId
              ? { ...booking, status: 'Cancelled' }
              : booking
          )
        );
      } else {
        throw new Error(response?.message || 'Không thể hủy đặt phòng');
      }
    } catch (error: any) {
      console.error('Error cancelling booking:', error);

      // Xử lý lỗi chi tiết hơn
      if (error.response && error.response.data) {
        console.error('API response error:', error.response.data);
        toast.error(error.response.data.message || 'Không thể hủy đặt phòng');
      } else {
        toast.error(error.message || 'Không thể hủy đặt phòng');
      }

      // Tải lại danh sách đặt phòng để đảm bảo dữ liệu đồng bộ
      fetchBookings();
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch (error) {
      return dateString;
    }
  };

  const getStatusClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'confirmed':
        return styles.statusConfirmed;
      case 'pending':
        return styles.statusPending;
      case 'cancelled':
        return styles.statusCancelled;
      case 'completed':
        return styles.statusCompleted;
      default:
        return '';
    }
  };

  if (!isAuthenticated) {
    return null;
  }

  return (
    <>
      <Head>
        <title>Đặt phòng của tôi | Hotel Nhóm 1</title>
      </Head>
      <Navbar />
      <div className={styles.pageContainer}>
        <div className={styles.bookingHeader}>
          <h1>Đặt phòng của tôi</h1>
          <p>Quản lý các đặt phòng và lịch sử đặt phòng của bạn</p>
        </div>

        {loading ? (
          <div className={styles.loadingContainer}>
            <div className={styles.spinner}></div>
          </div>
        ) : bookings.length === 0 ? (
          <div className={styles.emptyState}>
            <h2>Bạn chưa có đặt phòng nào</h2>
            <p>Hãy đặt phòng ngay để trải nghiệm dịch vụ của chúng tôi</p>
            <button
              className={styles.bookNowButton}
              onClick={() => router.push('/room/room')}
            >
              Đặt phòng ngay
            </button>
          </div>
        ) : (
          <div className={styles.bookingList}>
            {bookings.map((booking) => (
              <div key={booking.bookingId} className={styles.bookingCard}>
                <div className={styles.bookingHeader}>
                  <h3>Đặt phòng #{booking.bookingId}</h3>
                  <span className={`${styles.statusBadge} ${getStatusClass(booking.status)}`}>
                    {booking.status}
                  </span>
                </div>

                <div className={styles.bookingDetails}>
                  <div className={styles.detailRow}>
                    <span className={styles.detailLabel}>Loại phòng:</span>
                    <span className={styles.detailValue}>{booking.roomType}</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span className={styles.detailLabel}>Ngày nhận phòng:</span>
                    <span className={styles.detailValue}>{formatDate(booking.checkInDate)}</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span className={styles.detailLabel}>Ngày trả phòng:</span>
                    <span className={styles.detailValue}>{formatDate(booking.checkOutDate)}</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span className={styles.detailLabel}>Tổng tiền:</span>
                    <span className={styles.detailValue}>${booking.totalAmount}</span>
                  </div>
                  <div className={styles.detailRow}>
                    <span className={styles.detailLabel}>Ngày đặt:</span>
                    <span className={styles.detailValue}>{formatDate(booking.createdAt)}</span>
                  </div>
                </div>

                <div className={styles.bookingActions}>
                  <button
                    className={styles.viewDetailsButton}
                    onClick={() => router.push(`/booking/detail/${booking.bookingId}`)}
                  >
                    Xem chi tiết
                  </button>
                  {booking.status === 'Pending' || booking.status === 'Confirmed' ? (
                    <button
                      className={styles.cancelButton}
                      onClick={() => cancelBooking(booking.bookingId)}
                    >
                      Hủy đặt phòng
                    </button>
                  ) : null}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </>
  );
}
